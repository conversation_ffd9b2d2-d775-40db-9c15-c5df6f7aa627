import 'package:flutter/foundation.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

import '../core/utils/app_utils.dart';
import '../models/user_model.dart';
import '../models/agent_account_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import '../models/warehouse_model.dart';

class EnhancedPdfService {
  static final EnhancedPdfService _instance = EnhancedPdfService._internal();
  factory EnhancedPdfService() => _instance;
  EnhancedPdfService._internal();

  static EnhancedPdfService get instance => _instance;

  // Arabic font
  pw.Font? _arabicFont;
  pw.Font? _arabicFontBold;
  pw.Font? _englishFont;
  pw.Font? _englishFontBold;

  /// Initialize fonts with comprehensive fallback system
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing PDF fonts...');
    }

    // Try multiple font options in order of preference
    final fontOptions = [
      () async {
        _arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
        _arabicFontBold = await PdfGoogleFonts.notoSansArabicBold();
        _englishFont = await PdfGoogleFonts.robotoRegular();
        _englishFontBold = await PdfGoogleFonts.robotoBold();
        return 'Noto Sans Arabic + Roboto';
      },
      () async {
        _arabicFont = await PdfGoogleFonts.cairoRegular();
        _arabicFontBold = await PdfGoogleFonts.cairoBold();
        _englishFont = await PdfGoogleFonts.robotoRegular();
        _englishFontBold = await PdfGoogleFonts.robotoBold();
        return 'Cairo + Roboto';
      },
      () async {
        _arabicFont = await PdfGoogleFonts.robotoRegular();
        _arabicFontBold = await PdfGoogleFonts.robotoBold();
        _englishFont = await PdfGoogleFonts.robotoRegular();
        _englishFontBold = await PdfGoogleFonts.robotoBold();
        return 'Roboto';
      },
      () async {
        _arabicFont = await PdfGoogleFonts.openSansRegular();
        _arabicFontBold = await PdfGoogleFonts.openSansBold();
        _englishFont = await PdfGoogleFonts.robotoRegular();
        _englishFontBold = await PdfGoogleFonts.robotoBold();
        return 'Open Sans + Roboto';
      },
    ];

    String? loadedFont;
    for (final fontLoader in fontOptions) {
      try {
        loadedFont = await fontLoader();
        if (kDebugMode) {
          print('✅ Successfully loaded $loadedFont fonts');
        }
        break;
      } catch (e) {
        if (kDebugMode) {
          print('❌ Failed to load font: $e');
        }
        continue;
      }
    }

    // If all fonts fail, use system default
    if (_arabicFont == null || _arabicFontBold == null) {
      if (kDebugMode) {
        print('⚠️ All font loading attempts failed, using system defaults');
      }
      _arabicFont = null;
      _arabicFontBold = null;
      _englishFont = null;
      _englishFontBold = null;
    }
  }

  /// Create text style with proper font fallback for mixed content
  pw.TextStyle _createMixedTextStyle({
    double fontSize = 10,
    pw.FontWeight fontWeight = pw.FontWeight.normal,
    PdfColor color = PdfColors.black,
  }) {
    final isHeader = fontWeight == pw.FontWeight.bold;

    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      font: isHeader ? _arabicFontBold : _arabicFont,
      fontFallback: [
        // Arabic fonts first
        if (_arabicFont != null) _arabicFont!,
        if (_arabicFontBold != null) _arabicFontBold!,
        // English fonts for symbols and numbers
        if (_englishFont != null) _englishFont!,
        if (_englishFontBold != null) _englishFontBold!,
      ],
    );
  }

  /// Clean text for PDF display - fix symbols and mixed content
  String _cleanTextForPdf(String text) {
    return text
        .replaceAll('/', ' / ')  // Add spaces around forward slash
        .replaceAll('+', ' + ')  // Add spaces around plus
        .replaceAll('-', ' - ')  // Add spaces around dash
        .replaceAll('(', ' (')   // Add space before opening parenthesis
        .replaceAll(')', ') ')   // Add space after closing parenthesis
        .replaceAll('  ', ' ')   // Remove double spaces
        .trim();
  }

  /// Generate agent statement PDF
  Future<Uint8List> generateAgentStatementPDF({
    required UserModel agent,
    required AgentAccountModel account,
    required List<InvoiceModel> invoices,
    required List<PaymentModel> payments,
    required Map<String, dynamic> statistics,
  }) async {
    final pdf = pw.Document();

    // Ensure fonts are loaded
    if (_arabicFont == null || _arabicFontBold == null) {
      await initialize();
    }

    // Create theme with Arabic font support
    final theme = (_arabicFont != null && _arabicFontBold != null)
        ? pw.ThemeData.withFont(
            base: _arabicFont!,
            bold: _arabicFontBold!,
          )
        : pw.ThemeData.base();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        header: (context) => _buildModernHeader(
          title: 'كشف حساب الوكيل',
          subtitle: agent.fullName,
          date: DateTime.now(),
        ),
        footer: (context) => _buildModernFooter(context),
        build: (context) => [
          // Agent Info Section
          _buildAgentInfoSection(agent, account),
          pw.SizedBox(height: 20),

          // Explanation Section
          _buildExplanationSection(),
          pw.SizedBox(height: 15),

          // Statistics Section
          _buildStatisticsSection(statistics),
          pw.SizedBox(height: 20),

          // Transactions Table
          _buildTransactionsTable(invoices, payments),
          pw.SizedBox(height: 20),

          // Summary Section
          _buildSummarySection(account, statistics),
        ],
      ),
    );

    return pdf.save();
  }

  /// Generate warehouse movement report PDF
  Future<Uint8List> generateWarehouseMovementPDF({
    required List<WarehouseModel> warehouses,
    required List<Map<String, dynamic>> transfers,
    required List<Map<String, dynamic>> movements,
    required Map<String, dynamic> statistics,
  }) async {
    final pdf = pw.Document();

    // Ensure fonts are loaded
    if (_arabicFont == null || _arabicFontBold == null) {
      await initialize();
    }

    // Create theme with or without custom fonts
    final theme = (_arabicFont != null && _arabicFontBold != null)
        ? pw.ThemeData.withFont(
            base: _arabicFont!,
            bold: _arabicFontBold!,
          )
        : pw.ThemeData.base();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        header: (context) => _buildModernHeader(
          title: 'تقرير حركة المخازن',
          subtitle: 'تقرير شامل لجميع حركات المخازن',
          date: DateTime.now(),
        ),
        footer: (context) => _buildModernFooter(context),
        build: (context) => [
          // Statistics Overview
          _buildWarehouseStatistics(statistics),
          pw.SizedBox(height: 20),

          // Warehouses List
          _buildWarehousesList(warehouses),
          pw.SizedBox(height: 20),

          // Transfers Table
          if (transfers.isNotEmpty) ...[
            _buildTransfersTable(transfers),
            pw.SizedBox(height: 20),
          ],

          // Movements Table
          if (movements.isNotEmpty) ...[
            _buildMovementsTable(movements),
          ],
        ],
      ),
    );

    return pdf.save();
  }

  /// Generate agents list PDF
  Future<Uint8List> generateAgentsListPDF({
    required List<UserModel> agents,
    required List<AgentAccountModel> accounts,
    required Map<String, dynamic> statistics,
  }) async {
    final pdf = pw.Document();

    // Ensure fonts are loaded
    if (_arabicFont == null || _arabicFontBold == null) {
      await initialize();
    }

    // Create theme with or without custom fonts
    final theme = (_arabicFont != null && _arabicFontBold != null)
        ? pw.ThemeData.withFont(
            base: _arabicFont!,
            bold: _arabicFontBold!,
          )
        : pw.ThemeData.base();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        header: (context) => _buildModernHeader(
          title: 'قائمة الوكلاء',
          subtitle: 'قائمة شاملة بجميع الوكلاء وأرصدتهم',
          date: DateTime.now(),
        ),
        footer: (context) => _buildModernFooter(context),
        build: (context) => [
          // Statistics Overview
          _buildAgentsStatistics(statistics),
          pw.SizedBox(height: 20),

          // Agents Table
          _buildAgentsTable(agents, accounts),
          pw.SizedBox(height: 20),

          // Summary
          _buildAgentsSummary(statistics),
        ],
      ),
    );

    return pdf.save();
  }

  /// Build modern header
  pw.Widget _buildModernHeader({
    required String title,
    required String subtitle,
    required DateTime date,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        gradient: const pw.LinearGradient(
          colors: [PdfColors.blue800, PdfColors.blue600],
        ),
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                title,
                style: pw.TextStyle(
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white, // أبيض واضح للعناوين
                  font: _arabicFontBold,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                subtitle,
                style: pw.TextStyle(
                  fontSize: 14,
                  color: PdfColors.grey100, // أبيض مائل للرمادي للعناوين الفرعية
                  font: _arabicFont,
                ),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'آل فرحان للنقل الخفيف',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                  font: _arabicFontBold,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(
                AppUtils.formatDate(date),
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build modern footer
  pw.Widget _buildModernFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey300, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'صفحة ${context.pageNumber} من ${context.pagesCount}',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'تم إنشاؤه بواسطة تطبيق آل فرحان للنقل الخفيف',
            style: const pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build agent info section
  pw.Widget _buildAgentInfoSection(UserModel agent, AgentAccountModel account) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات الوكيل',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
              font: _arabicFontBold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoItem('الاسم', agent.fullName),
              ),
              pw.Expanded(
                child: _buildInfoItem('رقم الهاتف', agent.phone.isNotEmpty ? agent.phone : 'غير محدد'),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildInfoItem(
                  'الرصيد الحالي',
                  AppUtils.formatCurrency(account.currentBalance.abs()),
                  subtitle: account.currentBalance > 0 ? 'مديون للمؤسسة' : 'دائن لدى المؤسسة',
                  color: account.currentBalance > 0 ? PdfColors.red700 : PdfColors.green700,
                ),
              ),
              pw.Expanded(
                child: _buildInfoItem('تاريخ آخر تحديث',
                  AppUtils.formatDate(account.updatedAt)
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build info item with optional subtitle and color
  pw.Widget _buildInfoItem(String label, String value, {String? subtitle, PdfColor? color}) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          _cleanTextForPdf(label),
          style: _createMixedTextStyle(
            fontSize: 10,
            color: PdfColors.grey700, // تحسين التباين للتسميات
          ),
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          _cleanTextForPdf(value),
          style: _createMixedTextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: color ?? PdfColors.grey900, // لون أغمق للقيم لتحسين الوضوح
          ),
        ),
        if (subtitle != null) ...[
          pw.SizedBox(height: 2),
          pw.Text(
            _cleanTextForPdf(subtitle),
            style: _createMixedTextStyle(
              fontSize: 9,
              color: color ?? PdfColors.grey600,
            ),
          ),
        ],
      ],
    );
  }

  /// Build explanation section
  pw.Widget _buildExplanationSection() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'شرح كشف الحساب',
            style: _createMixedTextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            '• عند التحويل: الوكيل مديون بسعر الشراء فقط',
            style: _createMixedTextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            '• عند البيع: تُضاف حصة المؤسسة من الربح 50% للمديونية',
            style: _createMixedTextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            '• الدائن: المبالغ المدفوعة من الوكيل للمؤسسة',
            style: _createMixedTextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            '• الرصيد الصافي: المدين ناقص الدائن (موجب = مديون للمؤسسة، سالب = دائن لدى المؤسسة)',
            style: _createMixedTextStyle(
              fontSize: 10,
              color: PdfColors.grey700,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(4),
            ),
            child: pw.Text(
              'مثال: بضاعة بـ 1000 ج.م، بيعت بـ 1500 ج.م ← المديونية: 1000 + 250 (حصة المؤسسة) = 1250 ج.م',
              style: _createMixedTextStyle(
                fontSize: 9,
                color: PdfColors.grey600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build statistics section
  pw.Widget _buildStatisticsSection(Map<String, dynamic> statistics) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الإحصائيات',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(child: _buildStatCard('إجمالي المبيعات', AppUtils.formatCurrency(statistics['totalSales'] ?? 0), PdfColors.green)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('إجمالي الأرباح', AppUtils.formatCurrency(statistics['totalProfits'] ?? 0), PdfColors.orange)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('إجمالي المدفوع', AppUtils.formatCurrency(statistics['totalPaid'] ?? 0), PdfColors.blue)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build stat card
  pw.Widget _buildStatCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: color.shade(0.3)),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 10,
              color: PdfColors.grey600,
              font: _arabicFont,
            ),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: color,
              font: _arabicFontBold,
            ),
            textAlign: pw.TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Get item description from invoice
  String _getItemDescription(InvoiceModel invoice) {
    final customerName = invoice.customerName ?? 'عميل';
    return 'فاتورة رقم ${invoice.invoiceNumber} - $customerName';
  }

  /// Build transactions table
  pw.Widget _buildTransactionsTable(List<InvoiceModel> invoices, List<PaymentModel> payments) {
    final transactions = <Map<String, dynamic>>[];

    // Add transfer transactions (goods transferred from company to agent) - DEBIT
    for (final invoice in invoices) {
      // Add transfer transaction (goods given to agent) - DEBIT (always added)
      transactions.add({
        'date': invoice.createdAt,
        'type': 'تحويل بضاعة',
        'description': 'تحويل بضاعة من المؤسسة - ${_getItemDescription(invoice)}',
        'debit': invoice.itemCost,
        'credit': 0.0,
        'reference': invoice.invoiceNumber.length > 8 ? invoice.invoiceNumber.substring(0, 8) : invoice.invoiceNumber,
        'fullReference': invoice.invoiceNumber,
      });

      // If invoice is sold (completed), add company profit share - DEBIT
      if (invoice.status == 'completed' && invoice.companyProfitShare > 0) {
        transactions.add({
          'date': invoice.updatedAt,
          'type': 'حصة ربح المؤسسة',
          'description': 'حصة المؤسسة من ربح البيع - ${_getItemDescription(invoice)} (سعر البيع: ${AppUtils.formatCurrency(invoice.sellingPrice)})',
          'debit': invoice.companyProfitShare,
          'credit': 0.0,
          'reference': invoice.invoiceNumber.length > 8 ? invoice.invoiceNumber.substring(0, 8) : invoice.invoiceNumber,
          'fullReference': invoice.invoiceNumber,
        });
      }
    }

    // Add payments (money paid by agent to company) - CREDIT
    for (final payment in payments) {
      final description = (payment.notes?.isNotEmpty ?? false)
          ? 'دفعة نقدية - ${payment.notes}'
          : 'دفعة نقدية من الوكيل للمؤسسة';

      transactions.add({
        'date': payment.paymentDate,
        'type': 'دفعة',
        'description': description,
        'debit': 0.0,
        'credit': payment.amount,
        'reference': payment.receiptNumber.length > 8 ? payment.receiptNumber.substring(0, 8) : payment.receiptNumber,
        'fullReference': payment.receiptNumber,
      });
    }

    // Sort by date (oldest first)
    transactions.sort((a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'كشف حساب تفصيلي - جميع المعاملات',
          style: _createMixedTextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),

        // All transactions table with debit/credit format
        pw.Text(
          'كشف حساب الوكيل - ترتيب زمني',
          style: _createMixedTextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 5),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FixedColumnWidth(60), // التاريخ
            1: const pw.FixedColumnWidth(50), // النوع
            2: const pw.FlexColumnWidth(3), // الوصف
            3: const pw.FixedColumnWidth(60), // مدين
            4: const pw.FixedColumnWidth(60), // دائن
            5: const pw.FixedColumnWidth(50), // المرجع
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.blue100),
              children: [
                _buildTableCell('التاريخ', isHeader: true, fontSize: 9),
                _buildTableCell('النوع', isHeader: true, fontSize: 9),
                _buildTableCell('الوصف', isHeader: true, fontSize: 9),
                _buildTableCell('مدين ج.م', isHeader: true, fontSize: 9),
                _buildTableCell('دائن ج.م', isHeader: true, fontSize: 9),
                _buildTableCell('المرجع', isHeader: true, fontSize: 9),
              ],
            ),
            // All transaction data rows
            ...transactions.map((transaction) {
              final type = transaction['type'] as String;
              PdfColor typeColor;
              switch (type) {
                case 'دفعة':
                  typeColor = PdfColors.green700;
                  break;
                case 'تحويل':
                  typeColor = PdfColors.blue700;
                  break;
                case 'ربح بيع':
                  typeColor = PdfColors.orange700;
                  break;
                default:
                  typeColor = PdfColors.grey700;
                  break;
              }

              final debit = transaction['debit'] as double;
              final credit = transaction['credit'] as double;

              return pw.TableRow(
                children: [
                  _buildTableCell(AppUtils.formatDate(transaction['date']), fontSize: 8),
                  _buildTableCell(type, fontSize: 8, color: typeColor),
                  _buildTableCell(transaction['description'] ?? '-', fontSize: 8),
                  _buildTableCell(
                    debit > 0 ? AppUtils.formatCurrency(debit) : '-',
                    fontSize: 8,
                    color: debit > 0 ? PdfColors.red700 : PdfColors.grey600,
                  ),
                  _buildTableCell(
                    credit > 0 ? AppUtils.formatCurrency(credit) : '-',
                    fontSize: 8,
                    color: credit > 0 ? PdfColors.green700 : PdfColors.grey600,
                  ),
                  _buildTableCell(
                    transaction['reference'] ?? '-',
                    fontSize: 7,
                    color: PdfColors.grey700,
                  ),
                ],
              );
            }),
          ],
        ),
        pw.SizedBox(height: 15),

        // Summary section
        _buildAccountSummary(transactions),

        if (transactions.length > 20)
          pw.Padding(
            padding: const pw.EdgeInsets.only(top: 10),
            child: pw.Text(
              'عرض أول 20 معاملة من إجمالي ${transactions.length} معاملة',
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.grey600,
                fontStyle: pw.FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  pw.Widget _buildAccountSummary(List<Map<String, dynamic>> transactions) {
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (final transaction in transactions) {
      totalDebit += (transaction['debit'] as num?)?.toDouble() ?? 0.0;
      totalCredit += (transaction['credit'] as num?)?.toDouble() ?? 0.0;
    }

    // Net balance: positive means agent owes money (debt), negative means agent has credit
    final netBalance = totalDebit - totalCredit;

    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(8)),
        color: PdfColors.grey50,
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الحساب',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
              font: _arabicFontBold,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryItem(
                'إجمالي المدين',
                AppUtils.formatCurrency(totalDebit),
                PdfColors.red700,
              ),
              _buildSummaryItem(
                'إجمالي الدائن',
                AppUtils.formatCurrency(totalCredit),
                PdfColors.green700,
              ),
              _buildSummaryItem(
                'الرصيد الصافي',
                AppUtils.formatCurrency(netBalance.abs()),
                netBalance > 0 ? PdfColors.red700 : PdfColors.green700,
                subtitle: netBalance > 0 ? 'مديون للمؤسسة' : 'دائن لدى المؤسسة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  pw.Widget _buildSummaryItem(String title, String value, PdfColor color, {String? subtitle}) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.center,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontSize: 10,
            color: color,
            fontWeight: pw.FontWeight.normal,
            font: _arabicFont,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: color,
            font: _arabicFontBold,
          ),
        ),
        if (subtitle != null) ...[
          pw.SizedBox(height: 2),
          pw.Text(
            subtitle,
            style: pw.TextStyle(
              fontSize: 8,
              color: color,
              font: _arabicFont,
            ),
          ),
        ],
      ],
    );
  }

  /// Build table cell with improved contrast and alignment
  pw.Widget _buildTableCell(String text, {
    bool isHeader = false,
    PdfColor? color,
    double? fontSize,
    pw.TextAlign? textAlign,
  }) {
    // Clean text for better display
    final cleanText = _cleanTextForPdf(text);

    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        cleanText,
        style: _createMixedTextStyle(
          fontSize: fontSize ?? (isHeader ? 10 : 9),
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: color ?? (isHeader ? PdfColors.grey900 : PdfColors.grey800),
        ),
        textAlign: textAlign ?? pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl, // اتجاه النص من اليمين لليسار
      ),
    );
  }

  /// Build summary section with company profit analysis
  pw.Widget _buildSummarySection(AgentAccountModel account, Map<String, dynamic> statistics) {
    final totalSales = statistics['totalSales'] ?? 0.0;
    final totalProfits = statistics['totalProfits'] ?? 0.0;
    final totalAgentShare = statistics['totalAgentShare'] ?? 0.0;
    final totalCompanyShare = totalProfits - totalAgentShare;
    final companyProfitPercentage = totalProfits > 0 ? (totalCompanyShare / totalProfits) * 100 : 0;

    return pw.Column(
      children: [
        // Account Summary
        pw.Container(
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.grey300),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'ملخص حساب الوكيل',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('إجمالي الديون:', style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700, font: _arabicFont)),
                  pw.Text(
                    AppUtils.formatCurrency(account.totalDebt),
                    style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.grey900, font: _arabicFontBold),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('إجمالي المدفوع:', style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700, font: _arabicFont)),
                  pw.Text(
                    AppUtils.formatCurrency(account.totalPaid),
                    style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.grey900, font: _arabicFontBold),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Divider(color: PdfColors.grey400),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'الرصيد الحالي:',
                    style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.grey900, font: _arabicFontBold),
                  ),
                  pw.Text(
                    AppUtils.formatCurrency(account.currentBalance),
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                      color: account.currentBalance >= 0 ? PdfColors.green700 : PdfColors.red700,
                      font: _arabicFontBold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        pw.SizedBox(height: 15),

        // Company Profit Analysis
        pw.Container(
          padding: const pw.EdgeInsets.all(15),
          decoration: pw.BoxDecoration(
            color: PdfColors.red50,
            borderRadius: pw.BorderRadius.circular(8),
            border: pw.Border.all(color: PdfColors.red200),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'تحليل أرباح المؤسسة',
                style: pw.TextStyle(
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.red800,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('إجمالي المبيعات:', style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700, font: _arabicFont)),
                  pw.Text(
                    AppUtils.formatCurrency(totalSales),
                    style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.green700, font: _arabicFontBold),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('إجمالي الأرباح:', style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700, font: _arabicFont)),
                  pw.Text(
                    AppUtils.formatCurrency(totalProfits),
                    style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.blue700, font: _arabicFontBold),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('نصيب الوكيل:', style: pw.TextStyle(fontSize: 12, color: PdfColors.grey700, font: _arabicFont)),
                  pw.Text(
                    AppUtils.formatCurrency(totalAgentShare),
                    style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.orange700, font: _arabicFontBold),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Divider(color: PdfColors.red300),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'نصيب المؤسسة:',
                    style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.grey900, font: _arabicFontBold),
                  ),
                  pw.Text(
                    AppUtils.formatCurrency(totalCompanyShare),
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.red700,
                      font: _arabicFontBold,
                    ),
                  ),
                ],
              ),
              pw.SizedBox(height: 5),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'نسبة ربح المؤسسة:',
                    style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, color: PdfColors.grey900, font: _arabicFontBold),
                  ),
                  pw.Text(
                    '${companyProfitPercentage.toStringAsFixed(1)}%',
                    style: pw.TextStyle(
                      fontSize: 14,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.red700,
                      font: _arabicFontBold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build warehouse statistics
  pw.Widget _buildWarehouseStatistics(Map<String, dynamic> statistics) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'إحصائيات المخازن',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(child: _buildStatCard('إجمالي المخازن', '${statistics['totalWarehouses'] ?? 0}', PdfColors.blue)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('إجمالي التحويلات', '${statistics['totalTransfers'] ?? 0}', PdfColors.green)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('إجمالي الحركات', '${statistics['totalMovements'] ?? 0}', PdfColors.orange)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build warehouses list
  pw.Widget _buildWarehousesList(List<WarehouseModel> warehouses) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'قائمة المخازن',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: const {
            0: pw.FlexColumnWidth(2),
            1: pw.FlexColumnWidth(1),
            2: pw.FlexColumnWidth(2),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('اسم المخزن', isHeader: true),
                _buildTableCell('النوع', isHeader: true),
                _buildTableCell('العنوان', isHeader: true),
              ],
            ),
            // Data rows
            ...warehouses.map((warehouse) => pw.TableRow(
              children: [
                _buildTableCell(warehouse.name),
                _buildTableCell(warehouse.type),
                _buildTableCell(warehouse.address.isNotEmpty ? warehouse.address : 'غير محدد'),
              ],
            )),
          ],
        ),
      ],
    );
  }

  /// Build transfers table
  pw.Widget _buildTransfersTable(List<Map<String, dynamic>> transfers) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'أذونات التحويل',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: const {
            0: pw.FixedColumnWidth(80),
            1: pw.FlexColumnWidth(1),
            2: pw.FlexColumnWidth(1),
            3: pw.FlexColumnWidth(2),
            4: pw.FixedColumnWidth(60),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('التاريخ', isHeader: true),
                _buildTableCell('من مخزن', isHeader: true),
                _buildTableCell('إلى مخزن', isHeader: true),
                _buildTableCell('الصنف', isHeader: true),
                _buildTableCell('الكمية', isHeader: true),
              ],
            ),
            // Data rows
            ...transfers.take(15).map((transfer) => pw.TableRow(
              children: [
                _buildTableCell(AppUtils.formatDate(DateTime.parse(transfer['date']))),
                _buildTableCell(transfer['fromWarehouse'] ?? 'غير محدد'),
                _buildTableCell(transfer['toWarehouse'] ?? 'غير محدد'),
                _buildTableCell(transfer['itemName'] ?? 'غير محدد'),
                _buildTableCell('${transfer['quantity'] ?? 0}'),
              ],
            )),
          ],
        ),
      ],
    );
  }

  /// Build movements table
  pw.Widget _buildMovementsTable(List<Map<String, dynamic>> movements) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'حركة المخزون',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: const {
            0: pw.FixedColumnWidth(80),
            1: pw.FixedColumnWidth(60),
            2: pw.FlexColumnWidth(1),
            3: pw.FlexColumnWidth(2),
            4: pw.FixedColumnWidth(60),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('التاريخ', isHeader: true),
                _buildTableCell('النوع', isHeader: true),
                _buildTableCell('المخزن', isHeader: true),
                _buildTableCell('الصنف', isHeader: true),
                _buildTableCell('الكمية', isHeader: true),
              ],
            ),
            // Data rows
            ...movements.take(20).map((movement) => pw.TableRow(
              children: [
                _buildTableCell(_safeFormatDate(movement['date'])),
                _buildTableCell(movement['type']?.toString() ?? 'غير محدد'),
                _buildTableCell(movement['warehouse']?.toString() ?? 'غير محدد'),
                _buildTableCell(movement['itemName']?.toString() ?? 'غير محدد'),
                _buildTableCell('${movement['quantity'] ?? 0}'),
              ],
            )),
          ],
        ),
      ],
    );
  }

  /// Safe date formatting helper
  String _safeFormatDate(dynamic date) {
    try {
      if (date == null) return 'غير محدد';
      if (date is String) {
        final parsedDate = DateTime.parse(date);
        return AppUtils.formatDate(parsedDate);
      }
      if (date is DateTime) {
        return AppUtils.formatDate(date);
      }
      return 'غير محدد';
    } catch (e) {
      return 'غير محدد';
    }
  }

  /// Build agents statistics
  pw.Widget _buildAgentsStatistics(Map<String, dynamic> statistics) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.green200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'إحصائيات الوكلاء',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            children: [
              pw.Expanded(child: _buildStatCard('إجمالي الوكلاء', '${statistics['totalAgents'] ?? 0}', PdfColors.blue)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('الوكلاء النشطون', '${statistics['activeAgents'] ?? 0}', PdfColors.green)),
              pw.SizedBox(width: 10),
              pw.Expanded(child: _buildStatCard('إجمالي الأرصدة', AppUtils.formatCurrency(statistics['totalBalance'] ?? 0), PdfColors.orange)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build agents table
  pw.Widget _buildAgentsTable(List<UserModel> agents, List<AgentAccountModel> accounts) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'قائمة الوكلاء التفصيلية',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: const {
            0: pw.FlexColumnWidth(2),
            1: pw.FlexColumnWidth(1),
            2: pw.FixedColumnWidth(80),
            3: pw.FixedColumnWidth(80),
            4: pw.FixedColumnWidth(80),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('اسم الوكيل', isHeader: true),
                _buildTableCell('رقم الهاتف', isHeader: true),
                _buildTableCell('الرصيد الحالي', isHeader: true),
                _buildTableCell('إجمالي الديون', isHeader: true),
                _buildTableCell('إجمالي المدفوع', isHeader: true),
              ],
            ),
            // Data rows
            ...agents.map((agent) {
              final account = accounts.firstWhere(
                (a) => a.agentId == agent.id,
                orElse: () => AgentAccountModel(
                  id: agent.id,
                  agentId: agent.id,
                  agentName: agent.fullName,
                  agentPhone: agent.phone,
                  currentBalance: 0,
                  totalDebt: 0,
                  totalPaid: 0,
                  transactions: [],
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                  createdBy: 'system',
                ),
              );

              return pw.TableRow(
                children: [
                  _buildTableCell(agent.fullName),
                  _buildTableCell(agent.phone.isNotEmpty ? agent.phone : 'غير محدد'),
                  _buildTableCell(
                    AppUtils.formatCurrency(account.currentBalance),
                    color: account.currentBalance >= 0 ? PdfColors.green : PdfColors.red,
                  ),
                  _buildTableCell(AppUtils.formatCurrency(account.totalDebt)),
                  _buildTableCell(AppUtils.formatCurrency(account.totalPaid)),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// Build agents summary
  pw.Widget _buildAgentsSummary(Map<String, dynamic> statistics) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.grey300),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص عام',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('إجمالي الوكلاء:', style: pw.TextStyle(fontSize: 12, font: _arabicFont)),
              pw.Text(
                '${statistics['totalAgents'] ?? 0}',
                style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, font: _arabicFontBold),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('الوكلاء النشطون:', style: pw.TextStyle(fontSize: 12, font: _arabicFont)),
              pw.Text(
                '${statistics['activeAgents'] ?? 0}',
                style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold, font: _arabicFontBold),
              ),
            ],
          ),
          pw.SizedBox(height: 5),
          pw.Divider(color: PdfColors.grey400),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'إجمالي الأرصدة:',
                style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold, font: _arabicFontBold),
              ),
              pw.Text(
                AppUtils.formatCurrency(statistics['totalBalance'] ?? 0),
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: (statistics['totalBalance'] ?? 0) >= 0 ? PdfColors.green : PdfColors.red,
                  font: _arabicFontBold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Save and share PDF
  Future<void> saveAndSharePDF(Uint8List pdfBytes, String filename) async {
    try {
      // Use English filename to avoid file system issues
      final safeFilename = _createSafeFilename(filename);

      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: safeFilename,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sharing PDF: $e');
      }
      rethrow;
    }
  }

  /// Create a safe filename without Arabic characters
  String _createSafeFilename(String originalFilename) {
    // Replace Arabic text with English equivalents
    String safeFilename = originalFilename
        .replaceAll('كشف_حساب_', 'agent_statement_')
        .replaceAll('تقرير_حركة_المخازن_', 'warehouse_movement_report_')
        .replaceAll('قائمة_الوكلاء_', 'agents_list_')
        .replaceAll('/', '_')
        .replaceAll(':', '_')
        .replaceAll(' ', '_');

    // Ensure it ends with .pdf
    if (!safeFilename.toLowerCase().endsWith('.pdf')) {
      safeFilename += '.pdf';
    }

    return safeFilename;
  }
}
