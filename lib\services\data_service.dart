import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'firebase_service.dart';
import 'local_database_service.dart';
import 'auth_service.dart';
import '../models/user_model.dart';
import '../models/item_model.dart';
import '../models/warehouse_model.dart';
import '../models/document_tracking_model.dart';
import '../models/agent_account_model.dart';
import '../models/agent_transfer_invoice_model.dart';
import '../models/inventory_movement_model.dart';
import '../models/notification_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import '../core/constants/app_constants.dart';
import '../core/utils/app_utils.dart';
import 'composite_image_service.dart';
import 'enhanced_notification_service.dart';


class DataService {
  static DataService? _instance;
  static DataService get instance => _instance ??= DataService._();

  DataService._() {
    // Start periodic sync when service is initialized
    _startPeriodicSync();
  }

  final FirebaseService _firebaseService = FirebaseService.instance;
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  // final PerformanceMonitor _performanceMonitor = PerformanceMonitor.instance; // Not used currently
  final AuthService _authService = AuthService.instance;

  Timer? _syncTimer;

  // Generic method to check if online
  Future<bool> _isOnline() async {
    try {
      await _firebaseService.firestore.collection('_connectivity_test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Force sync from Firebase - makes Firebase the source of truth
  Future<void> forceSyncFromFirebase() async {
    if (!await _isOnline()) {
      if (kDebugMode) {
        print('Cannot sync: Device is offline');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('🔄 Starting force sync from Firebase...');
      }

      // Sync all collections
      await _forceSyncCollection('users', (doc) => UserModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('warehouses', (doc) => WarehouseModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('items', (doc) => ItemModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('invoices', (doc) => InvoiceModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('agent_accounts', (doc) => AgentAccountModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('payments', (doc) => PaymentModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('document_tracking', (doc) => DocumentTrackingModel.fromFirestore(doc).toMap());
      await _forceSyncCollection('notifications', (doc) => NotificationModel.fromFirestore(doc).toMap());

      // Clear local-only tables that don't exist in Firebase
      await _clearLocalOnlyTables();

      if (kDebugMode) {
        print('✅ Force sync from Firebase completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during force sync: $e');
      }
      rethrow;
    }
  }

  /// Force sync a specific collection from Firebase
  Future<void> _forceSyncCollection(String collectionName, Map<String, dynamic> Function(DocumentSnapshot) converter) async {
    try {
      // Get all documents from Firebase
      final snapshot = await _firebaseService.firestore.collection(collectionName).get();

      // Clear local collection
      await _localDb.delete(collectionName, '1=1', []);

      // Insert all Firebase documents
      for (final doc in snapshot.docs) {
        try {
          final data = converter(doc);
          data['syncStatus'] = 1; // Mark as synced
          await _localDb.insert(collectionName, data);
        } catch (e) {
          if (kDebugMode) {
            print('Error syncing document ${doc.id} from $collectionName: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Synced ${snapshot.docs.length} documents from $collectionName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing $collectionName: $e');
      }
    }
  }

  // Track last sync times for each collection
  final Map<String, DateTime> _lastSyncTimes = {};

  // Check if we should sync from Firebase (every 5 minutes)
  bool _shouldSyncFromFirebase(String collection) {
    final lastSync = _lastSyncTimes[collection];
    if (lastSync == null) return true;
    return DateTime.now().difference(lastSync).inMinutes >= 5;
  }

  // Update last sync time
  void _updateLastSyncTime(String collection) {
    _lastSyncTimes[collection] = DateTime.now();
  }

  // Update local database from Firebase data
  Future<void> _updateLocalFromFirebase(String tableName, List<Map<String, dynamic>> firebaseData) async {
    // Clear local table
    await _localDb.delete(tableName, '1=1', []);

    // Insert Firebase data
    for (final data in firebaseData) {
      data['syncStatus'] = 1; // Mark as synced
      await _localDb.insert(tableName, data);
    }
  }

  // Start periodic sync every 10 minutes
  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(const Duration(minutes: 10), (timer) async {
      if (await _isOnline()) {
        try {
          if (kDebugMode) {
            print('🔄 Starting periodic sync from Firebase...');
          }

          // Sync critical collections
          await getUsers(forceFromFirebase: true);
          await getWarehouses(forceFromFirebase: true);
          await getItems(forceFromFirebase: true);

          // Sync other important collections
          await _syncInvoicesFromFirebase();
          await _syncAgentAccountsFromFirebase();
          await _syncPaymentsFromFirebase();

          if (kDebugMode) {
            print('✅ Periodic sync completed');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Periodic sync failed: $e');
          }
        }
      }
    });
  }

  // Stop periodic sync
  void stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  /// Manual sync from Firebase - for user-triggered refresh
  Future<void> syncFromFirebase() async {
    if (!await _isOnline()) {
      throw 'لا يوجد اتصال بالإنترنت';
    }

    try {
      if (kDebugMode) {
        print('🔄 Starting manual sync from Firebase...');
      }

      // Force sync all critical collections
      await getUsers(forceFromFirebase: true);
      await getWarehouses(forceFromFirebase: true);
      await getItems(forceFromFirebase: true);

      // Sync other important collections
      await _syncInvoicesFromFirebase();
      await _syncAgentAccountsFromFirebase();
      await _syncPaymentsFromFirebase();

      // Clear local-only data
      await _clearLocalOnlyTables();

      if (kDebugMode) {
        print('✅ Manual sync completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Manual sync failed: $e');
      }
      rethrow;
    }
  }

  // Users operations
  Future<List<UserModel>> getUsers({String? role, bool? isActive, bool forceFromFirebase = false}) async {
    try {
      List<UserModel> users = [];

      // If online and (forced or periodic sync), fetch from Firebase first
      if (await _isOnline() && (forceFromFirebase || _shouldSyncFromFirebase('users'))) {
        try {
          Query query = _firebaseService.firestore.collection(AppConstants.usersCollection);

          if (role != null) {
            query = query.where('role', isEqualTo: role);
          }

          if (isActive != null) {
            query = query.where('isActive', isEqualTo: isActive);
          }

          final snapshot = await query.orderBy('fullName').get();
          users = snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

          // ALWAYS update local database with Firebase data (even if empty)
          await _updateLocalFromFirebase('users', users.map((u) => u.toMap()).toList());

          _updateLastSyncTime('users');

          if (kDebugMode) {
            print('✅ Synced ${users.length} users from Firebase (cleared local and updated)');
          }

          // Apply filters to Firebase results if needed
          if (role != null || isActive != null) {
            users = users.where((user) {
              bool matchesRole = role == null || user.role == role;
              bool matchesActive = isActive == null || user.isActive == isActive;
              return matchesRole && matchesActive;
            }).toList();
          }

          return users;
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error fetching from Firebase, falling back to local: $e');
          }
        }
      }

      // Fallback to local database
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (role != null) {
        whereClause += 'role = ?';
        whereArgs.add(role);
      }

      if (isActive != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'isActive = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final localUsers = await _localDb.query(
        'users',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'fullName ASC',
      );

      users = localUsers.map((userData) => UserModel.fromMap(userData)).toList();

      if (kDebugMode) {
        print('📱 Loaded ${users.length} users from local database');
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting users: $e');
      }
      rethrow;
    }
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      // Validate userId
      if (userId.isEmpty) {
        if (kDebugMode) {
          print('Error getting user by ID: userId is empty');
        }
        return null;
      }

      // Try local database first
      final localUsers = await _localDb.query('users', where: 'id = ?', whereArgs: [userId]);

      if (localUsers.isNotEmpty) {
        return UserModel.fromMap(localUsers.first);
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .doc(userId)
            .get();

        if (doc.exists) {
          final user = UserModel.fromFirestore(doc);
          await _localDb.insert('users', user.toMap());
          return user;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user by ID: $e');
      }
      return null;
    }
  }

  // Warehouses operations
  Future<List<WarehouseModel>> getWarehouses({String? type, bool? isActive, bool forceFromFirebase = false}) async {
    try {
      List<WarehouseModel> warehouses = [];

      // If online and (forced or periodic sync), fetch from Firebase first
      if (await _isOnline() && (forceFromFirebase || _shouldSyncFromFirebase('warehouses'))) {
        try {
          // Get ALL warehouses from Firebase first (no filters to avoid index issues)
          final snapshot = await _firebaseService.firestore.collection(AppConstants.warehousesCollection).get();
          warehouses = [];

          for (final doc in snapshot.docs) {
            try {
              final warehouse = WarehouseModel.fromFirestore(doc);
              warehouses.add(warehouse);
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing warehouse ${doc.id}: $e');
              }
              // Skip this warehouse and continue
            }
          }

          // ALWAYS update local database with Firebase data (even if empty)
          await _updateLocalFromFirebase('warehouses', warehouses.map((w) => w.toMap()).toList());

          _updateLastSyncTime('warehouses');

          if (kDebugMode) {
            print('✅ Synced ${warehouses.length} warehouses from Firebase (cleared local and updated)');
          }

          // Apply filters locally after syncing
          if (type != null || isActive != null) {
            warehouses = warehouses.where((warehouse) {
              bool matchesType = type == null || warehouse.type == type;
              bool matchesActive = isActive == null || warehouse.isActive == isActive;
              return matchesType && matchesActive;
            }).toList();
          }

          // Sort locally
          warehouses.sort((a, b) => a.name.compareTo(b.name));

          return warehouses;
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error fetching warehouses from Firebase, falling back to local: $e');
          }
        }
      }

      // Fallback to local database
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (type != null) {
        whereClause += 'type = ?';
        whereArgs.add(type);
      }

      if (isActive != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'isActive = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final localWarehouses = await _localDb.query(
        'warehouses',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'name ASC',
      );

      warehouses = localWarehouses.map((data) => WarehouseModel.fromMap(data)).toList();

      if (kDebugMode) {
        print('📱 Loaded ${warehouses.length} warehouses from local database');
      }
      
      return warehouses;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouses: $e');
      }
      rethrow;
    }
  }

  Future<WarehouseModel?> getWarehouseById(String warehouseId) async {
    try {
      // Try local database first
      final localWarehouses = await _localDb.query('warehouses', where: 'id = ?', whereArgs: [warehouseId]);

      if (localWarehouses.isNotEmpty) {
        try {
          return WarehouseModel.fromMap(localWarehouses.first);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing warehouse from local DB: $e');
            print('Warehouse data: ${localWarehouses.first}');
          }
          // Continue to try Firebase
        }
      }
      
      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.firestore
            .collection(AppConstants.warehousesCollection)
            .doc(warehouseId)
            .get();
        
        if (doc.exists) {
          final warehouse = WarehouseModel.fromFirestore(doc);
          await _localDb.insert('warehouses', warehouse.toMap());
          return warehouse;
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse by ID: $e');
      }
      return null;
    }
  }

  // Items operations
  Future<List<ItemModel>> getItems({
    String? warehouseId,
    String? status,
    String? type,
    int? limit,
    bool forceFromFirebase = false,
  }) async {
    try {
      List<ItemModel> items = [];

      // If online and (forced or periodic sync), fetch from Firebase first
      if (await _isOnline() && (forceFromFirebase || _shouldSyncFromFirebase('items'))) {
        try {
          // Get ALL items from Firebase first (no filters to avoid index issues)
          final snapshot = await _firebaseService.firestore.collection(AppConstants.itemsCollection).get();
          items = snapshot.docs.map((doc) => ItemModel.fromFirestore(doc)).toList();

          // ALWAYS update local database with Firebase data (even if empty)
          await _updateLocalFromFirebase('items', items.map((i) => i.toMap()).toList());

          _updateLastSyncTime('items');

          if (kDebugMode) {
            print('✅ Synced ${items.length} items from Firebase (cleared local and updated)');
          }

          // Apply filters locally after syncing
          if (warehouseId != null || status != null || type != null) {
            items = items.where((item) {
              bool matchesWarehouse = warehouseId == null || item.currentWarehouseId == warehouseId;
              bool matchesStatus = status == null || item.status == status;
              bool matchesType = type == null || item.type == type;
              return matchesWarehouse && matchesStatus && matchesType;
            }).toList();
          }

          // Sort locally
          items.sort((a, b) => b.createdAt.compareTo(a.createdAt));

          // Apply limit locally
          if (limit != null && items.length > limit) {
            items = items.take(limit).toList();
          }

          return items;
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error fetching items from Firebase, falling back to local: $e');
          }
        }
      }

      // Fallback to local database
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (warehouseId != null) {
        whereClause += 'currentWarehouseId = ?';
        whereArgs.add(warehouseId);
      }

      if (status != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'status = ?';
        whereArgs.add(status);
      }

      if (type != null) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += 'type = ?';
        whereArgs.add(type);
      }

      final localItems = await _localDb.query(
        'items',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'createdAt DESC',
        limit: limit,
      );

      items = localItems.map((data) => ItemModel.fromMap(data)).toList();

      if (kDebugMode) {
        print('📱 Loaded ${items.length} items from local database');
      }

      return items;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting items: $e');
      }
      rethrow;
    }
  }

  Future<ItemModel?> getItemById(String itemId) async {
    try {
      // Try local database first
      final localItems = await _localDb.query('items', where: 'id = ?', whereArgs: [itemId]);
      
      if (localItems.isNotEmpty) {
        return ItemModel.fromMap(localItems.first);
      }
      
      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .doc(itemId)
            .get();
        
        if (doc.exists) {
          final item = ItemModel.fromFirestore(doc);
          await _localDb.insert('items', item.toMap());
          return item;
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting item by ID: $e');
      }
      return null;
    }
  }

  // Search items by motor fingerprint text
  Future<List<ItemModel>> searchItemsByFingerprint(String searchText) async {
    try {
      // Search in local database first
      final localItems = await _localDb.query(
        'items',
        where: 'motorFingerprintText LIKE ?',
        whereArgs: ['%$searchText%'],
        orderBy: 'createdAt DESC',
      );
      
      List<ItemModel> items = localItems.map((data) => ItemModel.fromMap(data)).toList();
      
      // If online, also search in Firebase (for more comprehensive results)
      if (await _isOnline()) {
        // Note: Firestore doesn't support LIKE queries, so we'll use array-contains or startsWith
        // This is a simplified search - in production, you might want to use Algolia or similar
        final snapshot = await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .where('motorFingerprintText', isGreaterThanOrEqualTo: searchText)
            .where('motorFingerprintText', isLessThan: '$searchText\uf8ff')
            .get();
        
        final firebaseItems = snapshot.docs.map((doc) => ItemModel.fromFirestore(doc)).toList();
        
        // Merge results and remove duplicates
        final allItems = <String, ItemModel>{};
        for (final item in items) {
          allItems[item.id] = item;
        }
        for (final item in firebaseItems) {
          allItems[item.id] = item;
        }
        
        items = allItems.values.toList();
        items.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
      
      return items;
    } catch (e) {
      if (kDebugMode) {
        print('Error searching items by fingerprint: $e');
      }
      rethrow;
    }
  }

  // Create new item
  Future<ItemModel> createItem(ItemModel item) async {
    try {
      // Check if current user can manage inventory
      if (!_authService.hasPermission('manage_inventory')) {
        throw 'ليس لديك صلاحية لإضافة أصناف جديدة';
      }

      // Validate item data
      await _validateItemData(item);

      // Check for duplicate identifiers
      await _checkDuplicateIdentifiers(item);

      // Save to local database first
      await _localDb.insert('items', item.toMap());

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .doc(item.id)
            .set(item.toFirestore());

        await _localDb.markAsSynced('items', item.id);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('items', item.id, 'INSERT', item.toMap());
      }

      if (kDebugMode) {
        print('Item created: ${item.id}');
      }

      return item;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating item: $e');
      }
      rethrow;
    }
  }

  // Update item
  Future<ItemModel> updateItem(ItemModel item) async {
    try {
      // Update in local database
      await _localDb.update('items', item.toMap(), 'id = ?', [item.id]);

      // If online, update in Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .doc(item.id)
            .update(item.toFirestore());

        await _localDb.markAsSynced('items', item.id);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('items', item.id, 'UPDATE', item.toMap());
      }

      if (kDebugMode) {
        print('Item updated: ${item.id}');
      }

      return item;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating item: $e');
      }
      rethrow;
    }
  }

  /// Validate item data before creation/update
  Future<void> _validateItemData(ItemModel item) async {
    // Check required fields
    if (item.motorFingerprintText.trim().isEmpty) {
      throw 'بصمة الموتور مطلوبة';
    }

    if (item.chassisNumber.trim().isEmpty) {
      throw 'رقم الشاسيه مطلوب';
    }

    if (item.type.trim().isEmpty) {
      throw 'نوع المركبة مطلوب';
    }

    if (item.brand.trim().isEmpty) {
      throw 'الماركة مطلوبة';
    }

    if (item.model.trim().isEmpty) {
      throw 'الموديل مطلوب';
    }

    if (item.purchasePrice <= 0) {
      throw 'سعر الشراء يجب أن يكون أكبر من صفر';
    }

    if (item.suggestedSellingPrice <= 0) {
      throw 'سعر البيع المقترح يجب أن يكون أكبر من صفر';
    }

    if (item.yearOfManufacture < 1900 || item.yearOfManufacture > DateTime.now().year + 1) {
      throw 'سنة الصنع غير صحيحة';
    }
  }

  /// Check for duplicate motor fingerprint or chassis number
  Future<void> _checkDuplicateIdentifiers(ItemModel item) async {
    try {
      // Check motor fingerprint
      final existingByFingerprint = await _findItemByMotorFingerprint(item.motorFingerprintText);
      if (existingByFingerprint != null && existingByFingerprint.id != item.id) {
        throw 'بصمة الموتور "${item.motorFingerprintText}" موجودة مسبقاً في الصنف: ${existingByFingerprint.brand} ${existingByFingerprint.model}';
      }

      // Check chassis number
      final existingByChassis = await _findItemByChassisNumber(item.chassisNumber);
      if (existingByChassis != null && existingByChassis.id != item.id) {
        throw 'رقم الشاسيه "${item.chassisNumber}" موجود مسبقاً في الصنف: ${existingByChassis.brand} ${existingByChassis.model}';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking duplicate identifiers: $e');
      }
      rethrow;
    }
  }

  /// Find item by motor fingerprint text
  Future<ItemModel?> _findItemByMotorFingerprint(String fingerprintText) async {
    try {
      // Search in local database first
      final localItems = await _localDb.query(
        'items',
        where: 'motorFingerprintText = ?',
        whereArgs: [fingerprintText.trim()]
      );

      if (localItems.isNotEmpty) {
        return ItemModel.fromMap(localItems.first);
      }

      // If online, search in Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .where('motorFingerprintText', isEqualTo: fingerprintText.trim())
            .limit(1)
            .get();

        if (snapshot.docs.isNotEmpty) {
          return ItemModel.fromFirestore(snapshot.docs.first);
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding item by motor fingerprint: $e');
      }
      return null;
    }
  }

  /// Find item by chassis number
  Future<ItemModel?> _findItemByChassisNumber(String chassisNumber) async {
    try {
      // Search in local database first
      final localItems = await _localDb.query(
        'items',
        where: 'chassisNumber = ?',
        whereArgs: [chassisNumber.trim()]
      );

      if (localItems.isNotEmpty) {
        return ItemModel.fromMap(localItems.first);
      }

      // If online, search in Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .where('chassisNumber', isEqualTo: chassisNumber.trim())
            .limit(1)
            .get();

        if (snapshot.docs.isNotEmpty) {
          return ItemModel.fromFirestore(snapshot.docs.first);
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding item by chassis number: $e');
      }
      return null;
    }
  }

  /// Transfer item between warehouses with automatic invoice generation for agents
  Future<void> transferItemToWarehouse({
    required String itemId,
    required String fromWarehouseId,
    required String toWarehouseId,
    required String transferredBy,
    String? notes,
  }) async {
    try {
      // Get item details
      final item = await getItemById(itemId);
      if (item == null) {
        throw 'الصنف غير موجود';
      }

      // Get warehouse details
      final fromWarehouse = await getWarehouseById(fromWarehouseId);
      final toWarehouse = await getWarehouseById(toWarehouseId);

      if (fromWarehouse == null || toWarehouse == null) {
        throw 'المخزن غير موجود';
      }

      // Validate transfer
      if (item.currentWarehouseId != fromWarehouseId) {
        throw 'الصنف غير موجود في المخزن المحدد';
      }

      if (fromWarehouseId == toWarehouseId) {
        throw 'لا يمكن تحويل الصنف لنفس المخزن';
      }

      // Update item warehouse
      final updatedItem = item.copyWith(
        currentWarehouseId: toWarehouseId,
        updatedAt: DateTime.now(),
      );

      await updateItem(updatedItem);

      // If transferring to agent warehouse, create invoice
      if (toWarehouse.type == AppConstants.agentWarehouse) {
        await _createAgentTransferInvoice(
          item: updatedItem,
          agentWarehouse: toWarehouse,
          transferredBy: transferredBy,
          notes: notes,
        );
      }

      // Create transfer record
      await _createTransferRecord(
        item: updatedItem,
        fromWarehouse: fromWarehouse,
        toWarehouse: toWarehouse,
        transferredBy: transferredBy,
        notes: notes,
      );

      // Record inventory movement for reports
      final movement = InventoryMovementModel(
        id: '${DateTime.now().millisecondsSinceEpoch}_transfer_${item.id}',
        itemId: item.id,
        itemName: '${item.brand} ${item.model}',
        brand: item.brand,
        model: item.model,
        movementType: 'transfer',
        quantity: 1,
        sourceWarehouseId: fromWarehouseId,
        targetWarehouseId: toWarehouseId,
        sourceWarehouseName: fromWarehouse.name,
        targetWarehouseName: toWarehouse.name,
        reason: 'transfer',
        timestamp: DateTime.now(),
        createdBy: transferredBy,
        notes: notes,
      );

      await recordInventoryMovement(movement);

      if (kDebugMode) {
        print('Item transferred: ${item.id} from ${fromWarehouse.name} to ${toWarehouse.name}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error transferring item: $e');
      }
      rethrow;
    }
  }

  /// Create invoice for agent when item is transferred to their warehouse
  Future<void> _createAgentTransferInvoice({
    required ItemModel item,
    required WarehouseModel agentWarehouse,
    required String transferredBy,
    String? notes,
  }) async {
    try {
      // Find agent user for this warehouse
      final agentUser = await _findAgentByWarehouseId(agentWarehouse.id);
      if (agentUser == null) {
        throw 'لم يتم العثور على الوكيل المرتبط بالمخزن';
      }

      final now = DateTime.now();

      // Create agent invoice
      final invoice = InvoiceModel(
        id: AppUtils.generateId(),
        invoiceNumber: AppUtils.generateInvoiceId('INV-AGENT'),
        type: AppConstants.agentInvoice,
        agentId: agentUser.id,
        warehouseId: agentWarehouse.id,
        itemId: item.id,
        itemCost: item.purchasePrice,
        sellingPrice: item.purchasePrice, // Agent pays cost price
        profitAmount: 0.0, // No profit on transfer
        companyProfitShare: 0.0,
        agentProfitShare: 0.0,
        customerData: {
          'transferType': 'warehouse_transfer',
          'notes': notes ?? 'تحويل بضاعة للوكيل',
        },
        status: 'confirmed',
        createdAt: now,
        updatedAt: now,
        createdBy: transferredBy,
      );

      await createInvoice(invoice);

      // Update agent account balance
      final debtTransaction = AgentTransaction(
        id: AppUtils.generateId(),
        type: 'debt',
        amount: item.purchasePrice,
        description: 'تحويل بضاعة - ${item.brand} ${item.model}',
        invoiceId: invoice.id,
        itemId: item.id,
        timestamp: now,
        createdBy: transferredBy,
      );

      await addAgentTransaction(agentUser.id, debtTransaction);

      if (kDebugMode) {
        print('Agent transfer invoice created: ${invoice.invoiceNumber}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error creating agent transfer invoice: $e');
      }
      rethrow;
    }
  }

  /// Find agent user by warehouse ID
  Future<UserModel?> _findAgentByWarehouseId(String warehouseId) async {
    try {
      final users = await getUsers(role: AppConstants.agentRole);
      for (final user in users) {
        if (user.warehouseId == warehouseId) {
          return user;
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding agent by warehouse ID: $e');
      }
      return null;
    }
  }

  /// Create transfer record for audit trail
  Future<void> _createTransferRecord({
    required ItemModel item,
    required WarehouseModel fromWarehouse,
    required WarehouseModel toWarehouse,
    required String transferredBy,
    String? notes,
  }) async {
    try {
      final transferRecord = {
        'id': AppUtils.generateId(),
        'itemId': item.id,
        'fromWarehouseId': fromWarehouse.id,
        'fromWarehouseName': fromWarehouse.name,
        'toWarehouseId': toWarehouse.id,
        'toWarehouseName': toWarehouse.name,
        'transferredBy': transferredBy,
        'transferDate': DateTime.now().toIso8601String(),
        'notes': notes ?? '',
        'itemDetails': {
          'brand': item.brand,
          'model': item.model,
          'color': item.color,
          'motorFingerprint': item.motorFingerprintText,
          'chassisNumber': item.chassisNumber,
        },
      };

      // Save to local database
      await _localDb.insert('item_transfers', transferRecord);

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection('item_transfers')
            .doc(transferRecord['id'] as String)
            .set(transferRecord);
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error creating transfer record: $e');
      }
      // Don't rethrow as this shouldn't block the transfer
    }
  }

  // Get user's accessible warehouses based on role
  Future<List<WarehouseModel>> getUserAccessibleWarehouses() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('No current user found for warehouse access');
        }
        return [];
      }

      // First try to get warehouses from local database
      List<WarehouseModel> warehouses = await getWarehouses(isActive: true);

      // If no warehouses found locally, try to sync from Firebase
      if (warehouses.isEmpty && await _isOnline()) {
        try {
          await _syncWarehousesFromFirebase();
          warehouses = await getWarehouses(isActive: true);
        } catch (e) {
          debugPrint('Failed to sync warehouses from Firebase: $e');
        }
      }

      // Filter warehouses based on user role
      if (currentUser.isAgent) {
        // Agents can only see their own warehouse
        warehouses = warehouses.where((w) =>
          w.isAgentWarehouse && w.ownerId == currentUser.id).toList();

        if (kDebugMode) {
          print('Agent ${currentUser.fullName} has access to ${warehouses.length} warehouses');
        }
      } else if (currentUser.isAdmin || currentUser.isSuperAdmin) {
        // Admins and super admins can see all warehouses
        if (kDebugMode) {
          print('Admin/Super admin ${currentUser.fullName} has access to all ${warehouses.length} warehouses');
        }
      }

      if (kDebugMode) {
        print('Loaded ${warehouses.length} accessible warehouses for ${currentUser.role}');
        for (final warehouse in warehouses) {
          print('- ${warehouse.name} (${warehouse.type}) - ${warehouse.id}');
        }
      }

      return warehouses;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user accessible warehouses: $e');
      }
      return [];
    }
  }

  /// Sync warehouses from Firebase to local database
  Future<void> _syncWarehousesFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore
          .collection(AppConstants.warehousesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      for (final doc in snapshot.docs) {
        final warehouse = WarehouseModel.fromFirestore(doc);

        // Insert or update warehouse in local database
        try {
          await _localDb.insert('warehouses', warehouse.toMap());
        } catch (e) {
          // If insert fails (duplicate), try update
          await _localDb.update(
            'warehouses',
            warehouse.toMap(),
            'id = ?',
            [warehouse.id],
          );
        }
      }

      if (kDebugMode) {
        print('Synced ${snapshot.docs.length} warehouses from Firebase');
      }
    } catch (e) {
      debugPrint('Error syncing warehouses from Firebase: $e');
      rethrow;
    }
  }

  /// Get warehouses with permission filtering
  Future<List<WarehouseModel>> getWarehousesWithPermissions({String? type, bool? isActive}) async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) return [];

    // For agents, always return only their warehouse
    if (currentUser.isAgent) {
      return await getUserAccessibleWarehouses();
    }

    // For other roles, use the original method
    return await getWarehouses(type: type, isActive: isActive);
  }







  // Document tracking operations
  Future<String> createDocumentTracking(DocumentTrackingModel documentTracking) async {
    try {
      // Save to local database first
      await _localDb.insert('document_tracking', documentTracking.toMap());

      // If online, save to Firebase
      if (await _isOnline()) {
        final docRef = await _firebaseService.firestore
            .collection(AppConstants.documentTrackingCollection)
            .add(documentTracking.toFirestore());

        // Update local record with Firebase ID
        await _localDb.update(
          'document_tracking',
          {'id': docRef.id},
          'itemId = ? AND invoiceId = ?',
          [documentTracking.itemId, documentTracking.invoiceId]
        );

        await _localDb.markAsSynced('document_tracking', docRef.id);

        if (kDebugMode) {
          print('Document tracking created with ID: ${docRef.id}');
        }

        return docRef.id;
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('document_tracking', documentTracking.id, 'INSERT', documentTracking.toMap());
        return documentTracking.id;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating document tracking: $e');
      }
      rethrow;
    }
  }

  // Regenerate composite image for existing document tracking
  Future<void> regenerateCompositeImageForTracking(String trackingId) async {
    try {
      if (kDebugMode) {
        print('🔄 Regenerating composite image for tracking: $trackingId');
      }

      // Get the document tracking
      final results = await _localDb.query('document_tracking', where: 'id = ?', whereArgs: [trackingId]);
      if (results.isEmpty) {
        if (kDebugMode) {
          print('❌ Document tracking not found: $trackingId');
        }
        return;
      }

      final tracking = DocumentTrackingModel.fromMap(results.first);

      // Get invoice and item details
      final invoice = await getInvoiceById(tracking.invoiceId);
      final item = await getItemById(tracking.itemId);

      if (invoice == null || item == null) {
        if (kDebugMode) {
          print('❌ Missing invoice or item for tracking: $trackingId');
          print('   Invoice: ${invoice?.id ?? 'null'}');
          print('   Item: ${item?.id ?? 'null'}');
        }
        return;
      }

      String? compositeImagePath;

      // Create composite image
      final customerIdImages = invoice.customerIdImages;
      if (customerIdImages != null && customerIdImages.isNotEmpty) {
        final customerIdImagePath = customerIdImages.first;
        final motorFingerprintPath = item.motorFingerprintImageUrl;
        final chassisPath = item.chassisImageUrl;

        if (motorFingerprintPath.isNotEmpty && customerIdImagePath.isNotEmpty) {
          try {
            final compositeService = CompositeImageService();
            final compositeImage = await compositeService.createCompositeImage(
              invoice: invoice,
              item: item,
              motorFingerprintImagePath: motorFingerprintPath,
              chassisImagePath: chassisPath.isNotEmpty ? chassisPath : '',
              customerIdImagePath: customerIdImagePath,
            );

            compositeImagePath = compositeImage.path;

            if (kDebugMode) {
              print('✅ Composite image regenerated: $compositeImagePath');
            }
          } catch (e) {
            if (kDebugMode) {
              print('❌ Failed to regenerate composite image: $e');
            }
          }
        }
      }

      // Update the document tracking with the new composite image path
      if (compositeImagePath != null) {
        final updatedTracking = DocumentTrackingModel(
          id: tracking.id,
          itemId: tracking.itemId,
          invoiceId: tracking.invoiceId,
          currentStatus: tracking.currentStatus,
          statusHistory: tracking.statusHistory,
          createdAt: tracking.createdAt,
          updatedAt: DateTime.now(),
          createdBy: tracking.createdBy,
          compositeImagePath: compositeImagePath,
          additionalData: tracking.additionalData,
        );

        // Update in local database
        final updateResult = await _localDb.update('document_tracking', updatedTracking.toMap(), 'id = ?', [trackingId]);

        if (kDebugMode) {
          print('📝 Database update result: $updateResult rows affected');

          // Verify the update
          final verifyResults = await _localDb.query('document_tracking', where: 'id = ?', whereArgs: [trackingId]);
          if (verifyResults.isNotEmpty) {
            final verifiedTracking = DocumentTrackingModel.fromMap(verifyResults.first);
            print('✅ Verified composite image path: ${verifiedTracking.compositeImagePath}');
          } else {
            print('❌ Failed to verify update - tracking not found');
          }
        }

        // Add to sync queue
        try {
          await _localDb.addToSyncQueue('document_tracking', trackingId, 'UPDATE', updatedTracking.toMap());
        } catch (e) {
          if (kDebugMode) {
            print('Error adding to sync queue: $e');
          }
        }

        if (kDebugMode) {
          print('✅ Document tracking updated with composite image: $trackingId');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error regenerating composite image for tracking: $e');
      }
    }
  }

  // Get document tracking by ID from local database only
  Future<DocumentTrackingModel?> getDocumentTrackingByIdLocal(String trackingId) async {
    try {
      final results = await _localDb.query('document_tracking', where: 'id = ?', whereArgs: [trackingId]);
      if (results.isNotEmpty) {
        return DocumentTrackingModel.fromMap(results.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting document tracking from local database: $e');
      }
      return null;
    }
  }

  // Update document tracking status with enhanced permissions and notifications
  Future<void> updateDocumentTrackingStatus({
    required String documentId,
    required String newStatus,
    required String updatedBy,
    String? notes,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 Attempting to update document tracking status:');
        print('   Document ID: $documentId');
        print('   New Status: $newStatus');
        print('   Updated By: $updatedBy');
        print('   Notes: $notes');
      }

      // Validate permissions for document status update
      await _validateDocumentStatusUpdatePermissions(documentId, newStatus, updatedBy);

      final now = DateTime.now();

      // Get current document tracking
      final localDocs = await _localDb.query(
        'document_tracking',
        where: 'id = ?',
        whereArgs: [documentId]
      );

      if (localDocs.isEmpty) {
        throw 'Document tracking not found';
      }

      final currentDoc = DocumentTrackingModel.fromMap(localDocs.first);

      // Validate status transition
      await _validateStatusTransition(currentDoc.currentStatus, newStatus);

      // Create new status history entry
      final newStatusHistory = DocumentStatusHistory(
        status: newStatus,
        timestamp: now,
        updatedBy: updatedBy,
        notes: notes,
      );

      // Update document tracking
      final updatedDoc = currentDoc.copyWith(
        currentStatus: newStatus,
        statusHistory: [...currentDoc.statusHistory, newStatusHistory],
        updatedAt: now,
      );

      // Update in local database
      await _localDb.update(
        'document_tracking',
        updatedDoc.toMap(),
        'id = ?',
        [documentId]
      );

      // If online, update in Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.documentTrackingCollection)
            .doc(documentId)
            .update(updatedDoc.toFirestore());

        await _localDb.markAsSynced('document_tracking', documentId);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('document_tracking', documentId, 'UPDATE', updatedDoc.toMap());
      }

      // Send notifications about status change
      await _sendDocumentStatusNotifications(currentDoc, newStatus, updatedBy, notes);

      if (kDebugMode) {
        print('✅ Document tracking status updated successfully:');
        print('   Document ID: $documentId');
        print('   Old Status: ${currentDoc.currentStatus}');
        print('   New Status: $newStatus');
        print('   Updated By: $updatedBy');
        print('   Timestamp: ${DateTime.now()}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating document tracking status: $e');
      }
      rethrow;
    }
  }

  /// Validate permissions for document status updates
  Future<void> _validateDocumentStatusUpdatePermissions(
    String documentId,
    String newStatus,
    String updatedBy,
  ) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('❌ Permission check failed: No current user logged in');
        }
        throw 'يجب تسجيل الدخول أولاً';
      }

      if (kDebugMode) {
        print('🔐 Checking permissions for document status update:');
        print('   Current User: ${currentUser.fullName} (${currentUser.role})');
        print('   User ID: ${currentUser.id}');
        print('   Updated By: $updatedBy');
      }

      // Only managers (super admin and admin) can update document status
      if (currentUser.role != AppConstants.superAdminRole &&
          currentUser.role != AppConstants.adminRole) {
        if (kDebugMode) {
          print('❌ Permission denied: User role "${currentUser.role}" is not authorized');
          print('   Required roles: ${AppConstants.superAdminRole} or ${AppConstants.adminRole}');
        }
        throw 'فقط المديرون يمكنهم تحديث حالة الجواب';
      }

      // Verify the user updating is the current user
      if (currentUser.id != updatedBy) {
        if (kDebugMode) {
          print('❌ Permission denied: User ID mismatch');
          print('   Current User ID: ${currentUser.id}');
          print('   Updated By: $updatedBy');
        }
        throw 'لا يمكن تحديث الحالة باسم مستخدم آخر';
      }

      if (kDebugMode) {
        print('✅ Permission check passed: User authorized to update document status');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating document status update permissions: $e');
        final user = _authService.currentUser;
        print('Current user: ${user?.fullName} (${user?.role})');
        print('Document ID: $documentId');
        print('New status: $newStatus');
        print('Updated by: $updatedBy');
      }
      rethrow;
    }
  }

  /// Validate status transition logic
  Future<void> _validateStatusTransition(String currentStatus, String newStatus) async {
    try {
      // Define valid status transitions
      const validTransitions = {
        AppConstants.documentSentToManager: [AppConstants.documentSentToManufacturer],
        AppConstants.documentSentToManufacturer: [AppConstants.documentReceivedFromManufacturer],
        AppConstants.documentReceivedFromManufacturer: [AppConstants.documentDeliveredToAgent],
        AppConstants.documentDeliveredToAgent: [], // Final status
      };

      if (!validTransitions.containsKey(currentStatus)) {
        throw 'الحالة الحالية غير صحيحة: $currentStatus';
      }

      final allowedNextStatuses = validTransitions[currentStatus]!;
      if (!allowedNextStatuses.contains(newStatus)) {
        throw 'لا يمكن تغيير الحالة من "$currentStatus" إلى "$newStatus"';
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error validating status transition: $e');
      }
      rethrow;
    }
  }

  /// Send notifications when document status changes
  Future<void> _sendDocumentStatusNotifications(
    DocumentTrackingModel currentDoc,
    String newStatus,
    String updatedBy,
    String? notes,
  ) async {
    try {
      // Get invoice details to find the agent
      final invoice = await getInvoiceById(currentDoc.invoiceId);
      if (invoice == null) {
        if (kDebugMode) {
          print('Invoice not found for document tracking: ${currentDoc.invoiceId}');
        }
        return;
      }

      // Get item details
      final item = await getItemById(currentDoc.itemId);
      if (item == null) {
        if (kDebugMode) {
          print('Item not found for document tracking: ${currentDoc.itemId}');
        }
        return;
      }

      // Get updater details
      final updater = await getUserById(updatedBy);
      final updaterName = updater?.fullName ?? 'مدير النظام';

      // Get status display text
      final statusText = _getDocumentStatusDisplayText(newStatus);

      // Send notification to agent if invoice has agent
      if (invoice.agentId != null && invoice.agentId!.isNotEmpty) {
        await _sendNotificationToAgent(
          agentId: invoice.agentId!,
          title: 'تحديث حالة الجواب',
          message: 'تم تحديث حالة جواب ${item.brand} ${item.model} إلى: $statusText',
          data: {
            'type': 'document_status_update',
            'documentId': currentDoc.id,
            'itemId': item.id,
            'invoiceId': invoice.id,
            'oldStatus': currentDoc.currentStatus,
            'newStatus': newStatus,
            'updatedBy': updaterName,
            'notes': notes,
          },
        );
      }

      if (kDebugMode) {
        print('Document status notification sent: ${item.brand} ${item.model} -> $statusText');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error sending document status notifications: $e');
      }
      // Don't rethrow as this shouldn't block the status update
    }
  }

  /// Get display text for document status
  String _getDocumentStatusDisplayText(String status) {
    switch (status) {
      case 'sent_to_manufacturer':
        return 'تم إرسال الجواب للمصنع';
      case 'received_from_manufacturer':
        return 'تم استلام الجواب من المصنع';
      case 'sent_to_sale_point':
        return 'تم إرسال الجواب لنقطة البيع';
      case 'ready_for_pickup':
        return 'الجواب جاهز للاستلام';
      default:
        return status;
    }
  }

  /// Send notification to specific agent
  Future<void> _sendNotificationToAgent({
    required String agentId,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Log the notification
      if (kDebugMode) {
        print('Notification to agent $agentId: $title - $message');
      }

      // Create notification record
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: title,
        message: message,
        type: data?['type'] ?? 'general',
        targetUserId: agentId,
        isRead: false,
        createdAt: DateTime.now(),
        createdBy: _authService.currentUser?.id ?? 'system',
      );

      // Save to local database
      await _localDb.insert('notifications', notification.toMap());

      // Sync to Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance
              .collection('notifications')
              .doc(notification.id)
              .set(notification.toMap());
        } catch (e) {
          if (kDebugMode) {
            print('Failed to sync notification to Firebase: $e');
          }
        }
      }

      if (kDebugMode) {
        print('Notification saved for agent $agentId: $title');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error sending notification to agent: $e');
      }
    }
  }

  /// Send notification when document status changes
  Future<void> _sendDocumentStatusNotification(
    DocumentTrackingModel currentDoc,
    String newStatus,
    String updatedBy
  ) async {
    try {
      // Get the invoice to find the agent
      final invoiceData = await _localDb.query(
        'invoices',
        where: 'id = ?',
        whereArgs: [currentDoc.invoiceId],
      );

      if (invoiceData.isEmpty) return;

      final invoice = InvoiceModel.fromMap(invoiceData.first);

      // Get item details for notification
      final itemData = await _localDb.query(
        'items',
        where: 'id = ?',
        whereArgs: [currentDoc.itemId],
      );

      String itemName = currentDoc.itemId;
      if (itemData.isNotEmpty) {
        final item = ItemModel.fromMap(itemData.first);
        itemName = item.displayName;
      }

      // Create notification for the agent who sold the item
      final notification = NotificationService.createDocumentStatusNotification(
        documentId: currentDoc.id,
        itemName: itemName,
        oldStatus: currentDoc.currentStatus,
        newStatus: newStatus,
        agentId: invoice.createdBy, // The agent who created the invoice
        updatedBy: updatedBy,
      );

      await createNotification(notification);

      if (kDebugMode) {
        print('Document status notification sent to agent: ${invoice.createdBy}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sending document status notification: $e');
      }
      // Don't rethrow as this shouldn't block the status update
    }
  }

  // Create new invoice
  Future<String> createInvoice(InvoiceModel invoice) async {
    try {
      // Validate invoice creation permissions
      await _validateInvoiceCreationPermissions(invoice);

      // Generate unique ID if not provided
      final invoiceId = invoice.id.isEmpty ? AppUtils.generateId() : invoice.id;
      final invoiceWithId = invoice.copyWith(id: invoiceId);

      // Save to local database first
      final invoiceMap = invoiceWithId.toMap();
      await _localDb.insert('invoices', invoiceMap);

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.invoicesCollection)
            .doc(invoiceId)
            .set(invoiceWithId.toFirestore());

        await _localDb.markAsSynced('invoices', invoiceId);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('invoices', invoiceId, 'INSERT', invoiceWithId.toMap());
      }

      // Create document tracking for customer invoices
      if (invoice.type == 'customer') {
        await _createDocumentTracking(invoiceId, invoice.itemId, invoice.createdBy);
      }

      // Send notification to managers about new invoice
      await _notifyManagersNewInvoice(invoiceWithId);

      if (kDebugMode) {
        print('Invoice created with ID: $invoiceId');
      }

      return invoiceId;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating invoice: $e');
      }
      rethrow;
    }
  }

  /// Get invoice by ID
  Future<InvoiceModel?> getInvoiceById(String invoiceId) async {
    try {
      // Try local database first
      final localInvoices = await _localDb.query('invoices', where: 'id = ?', whereArgs: [invoiceId]);

      if (localInvoices.isNotEmpty) {
        return InvoiceModel.fromMap(localInvoices.first);
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.firestore
            .collection(AppConstants.invoicesCollection)
            .doc(invoiceId)
            .get();

        if (doc.exists) {
          final invoice = InvoiceModel.fromFirestore(doc);
          await _localDb.insert('invoices', invoice.toMap());
          return invoice;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoice by ID: $e');
      }
      return null;
    }
  }

  /// Validate invoice creation permissions based on user role and warehouse type
  Future<void> _validateInvoiceCreationPermissions(InvoiceModel invoice) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // Validate warehouseId first
      if (invoice.warehouseId.isEmpty) {
        throw 'معرف المخزن مطلوب';
      }

      // Get warehouse details using the safe method
      final warehouse = await _getWarehouseById(invoice.warehouseId);
      if (warehouse == null) {
        throw 'المخزن غير موجود';
      }

      // Check permissions based on user role and warehouse type
      switch (currentUser.role) {
        case AppConstants.superAdminRole:
          // Super admin can sell from any warehouse
          break;

        case AppConstants.adminRole:
          // Admin users cannot sell from agent warehouses
          if (warehouse.type == AppConstants.agentWarehouse) {
            throw 'المديرون الإداريون لا يمكنهم البيع من مخازن الوكلاء';
          }
          break;

        case AppConstants.agentRole:
          // Agents can only sell from their own warehouse
          if (warehouse.type != AppConstants.agentWarehouse ||
              warehouse.ownerId != currentUser.id) {
            throw 'الوكلاء يمكنهم البيع من مخازنهم الخاصة فقط';
          }
          break;

        case AppConstants.showroomRole:
          // Showroom users can only sell from showroom warehouses
          if (warehouse.type != AppConstants.showroomWarehouse) {
            throw 'مستخدمو المعرض يمكنهم البيع من مخازن المعرض فقط';
          }
          break;

        default:
          throw 'دور المستخدم غير صحيح';
      }

      // Additional validation for customer invoices
      if (invoice.type == AppConstants.customerInvoice) {
        // Ensure only sales-enabled warehouses can create customer invoices
        if (!warehouse.canSellGoods) {
          throw 'هذا المخزن لا يدعم البيع للعملاء';
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error validating invoice creation permissions: $e');
      }
      rethrow;
    }
  }

  /// Calculate profit distribution based on warehouse type, user role, and agent's profit share percentage
  Map<String, double> calculateProfitDistribution({
    required double sellingPrice,
    required double itemCost,
    required String warehouseType,
    required String userRole,
    double? agentProfitSharePercentage, // نسبة ربح الوكيل (0.0 إلى 1.0)
  }) {
    final profitAmount = sellingPrice - itemCost;

    double companyProfitShare = profitAmount;
    double agentProfitShare = 0.0;

    // Profit distribution rules
    if (warehouseType == AppConstants.agentWarehouse && userRole == AppConstants.agentRole) {
      // Agent selling from their warehouse: use agent's custom profit share percentage
      final agentPercentage = agentProfitSharePercentage ?? 0.5; // Default 50% if not provided
      agentProfitShare = profitAmount * agentPercentage;
      companyProfitShare = profitAmount * (1.0 - agentPercentage);

      if (kDebugMode) {
        print('💰 Profit distribution for agent sale:');
        print('   Total profit: ${AppUtils.formatCurrency(profitAmount)}');
        print('   Agent percentage: ${(agentPercentage * 100).toStringAsFixed(1)}%');
        print('   Agent share: ${AppUtils.formatCurrency(agentProfitShare)}');
        print('   Company share: ${AppUtils.formatCurrency(companyProfitShare)}');
      }
    } else if (warehouseType == AppConstants.showroomWarehouse) {
      // Showroom sales: 100% to company
      companyProfitShare = profitAmount;
      agentProfitShare = 0.0;
    } else if (warehouseType == AppConstants.mainWarehouse) {
      // Main warehouse sales (rare): 100% to company
      companyProfitShare = profitAmount;
      agentProfitShare = 0.0;
    }

    return {
      'totalProfit': profitAmount,
      'companyShare': companyProfitShare,
      'agentShare': agentProfitShare,
    };
  }

  /// Validate sale transaction before processing
  Future<void> validateSaleTransaction({
    required String itemId,
    required double sellingPrice,
    required String warehouseId,
    required String buyerId,
  }) async {
    try {
      // Get item details
      final item = await getItemById(itemId);
      if (item == null) {
        throw 'الصنف غير موجود';
      }

      // Check if item is available for sale
      if (!item.isAvailable) {
        throw 'الصنف غير متاح للبيع (الحالة: ${item.status})';
      }

      // Check if item is in the specified warehouse
      if (item.currentWarehouseId != warehouseId) {
        throw 'الصنف غير موجود في المخزن المحدد';
      }

      // Validate selling price
      if (sellingPrice <= 0) {
        throw 'سعر البيع يجب أن يكون أكبر من صفر';
      }

      if (sellingPrice < item.purchasePrice) {
        throw 'سعر البيع لا يمكن أن يكون أقل من سعر الشراء';
      }

      // Check warehouse permissions
      final warehouse = await getWarehouseById(warehouseId);
      if (warehouse == null) {
        throw 'المخزن غير موجود';
      }

      if (!warehouse.canSellGoods) {
        throw 'هذا المخزن لا يدعم عمليات البيع';
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error validating sale transaction: $e');
      }
      rethrow;
    }
  }

  /// Process sale transaction with automatic profit calculation and agent account update
  Future<String> processSaleTransaction({
    required String itemId,
    required double sellingPrice,
    required Map<String, dynamic> customerData,
    List<String>? customerIdImages,
    String? notes,
  }) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // Get item details
      final item = await getItemById(itemId);
      if (item == null) {
        throw 'الصنف غير موجود';
      }

      // Validate the sale
      await validateSaleTransaction(
        itemId: itemId,
        sellingPrice: sellingPrice,
        warehouseId: item.currentWarehouseId,
        buyerId: currentUser.id,
      );

      // Get warehouse details
      final warehouse = await getWarehouseById(item.currentWarehouseId);
      if (warehouse == null) {
        throw 'المخزن غير موجود';
      }

      // Calculate profit distribution using agent's custom profit share percentage
      final profitDistribution = calculateProfitDistribution(
        sellingPrice: sellingPrice,
        itemCost: item.purchasePrice,
        warehouseType: warehouse.type,
        userRole: currentUser.role,
        agentProfitSharePercentage: currentUser.profitSharePercentage,
      );

      final now = DateTime.now();

      // Create invoice
      final invoice = InvoiceModel(
        id: AppUtils.generateId(),
        invoiceNumber: AppUtils.generateInvoiceId('INV-CUSTOMER'),
        type: AppConstants.customerInvoice,
        agentId: currentUser.isAgent ? currentUser.id : null,
        warehouseId: item.currentWarehouseId,
        itemId: item.id,
        itemCost: item.purchasePrice,
        sellingPrice: sellingPrice,
        profitAmount: profitDistribution['totalProfit']!,
        companyProfitShare: profitDistribution['companyShare']!,
        agentProfitShare: profitDistribution['agentShare']!,
        status: 'confirmed',
        createdAt: now,
        updatedAt: now,
        createdBy: currentUser.id,
        customerData: customerData,
        customerIdImages: customerIdImages,
        additionalData: {
          'warehouseType': warehouse.type,
          'notes': notes,
        },
      );

      // Save invoice
      final invoiceId = await createInvoice(invoice);

      // Update item status to sold with sales invoice data
      final updatedItem = item.copyWith(
        status: 'مباع',
        updatedAt: now,
        additionalData: {
          ...?item.additionalData,
          'salesInvoiceId': invoiceId,
          'salesLocation': warehouse.name,
          'salesWarehouseId': warehouse.id,
          'salesWarehouseType': warehouse.type,
          'soldAt': now.toIso8601String(),
          'soldBy': currentUser.id,
          'sellingPrice': sellingPrice,
        },
      );
      await updateItem(updatedItem);

      // Update agent account if applicable
      if (currentUser.isAgent) {
        // Only add company profit share as debt
        // The item cost was already added when item was transferred to agent

        // Add company profit share as additional debt
        if (profitDistribution['companyShare']! > 0) {
          final profitTransaction = AgentTransaction(
            id: AppUtils.generateId(),
            type: 'debt',
            amount: profitDistribution['companyShare']!,
            description: 'نصيب المؤسسة من الربح - ${item.brand} ${item.model} (50%)',
            invoiceId: invoiceId,
            itemId: item.id,
            timestamp: now,
            createdBy: currentUser.id,
          );

          await addAgentTransaction(currentUser.id, profitTransaction);
        }
      }

      if (kDebugMode) {
        print('Sale processed successfully: Invoice $invoiceId');
        print('Profit distribution: ${profitDistribution.toString()}');
      }

      return invoiceId;

    } catch (e) {
      if (kDebugMode) {
        print('Error processing sale transaction: $e');
      }
      rethrow;
    }
  }

  // Create document tracking for an invoice
  Future<void> _createDocumentTracking(String invoiceId, String itemId, String createdBy) async {
    try {
      final trackingId = '${DateTime.now().millisecondsSinceEpoch}_${itemId.hashCode}';
      final now = DateTime.now();

      // Get invoice and item details for composite image
      final invoice = await getInvoiceById(invoiceId);
      final item = await getItemById(itemId);

      String? compositeImagePath;

      // Create composite image if we have the required data
      if (invoice != null && item != null) {
        if (kDebugMode) {
          print('🖼️ Checking composite image requirements:');
          print('   Invoice ID: ${invoice.id}');
          print('   Item ID: ${item.id}');
        }

        try {
          // Check if we have the required images for composite
          final customerIdImages = invoice.customerIdImages;
          if (kDebugMode) {
            print('   Customer ID Images: ${customerIdImages?.length ?? 0} images');
            if (customerIdImages != null && customerIdImages.isNotEmpty) {
              print('   First customer ID image: ${customerIdImages.first}');
            }
          }

          if (customerIdImages != null && customerIdImages.isNotEmpty) {
            // Get the first customer ID image (front side)
            final customerIdImagePath = customerIdImages.first;

            // Get motor fingerprint and chassis image URLs from item
            final motorFingerprintPath = item.motorFingerprintImageUrl;
            final chassisPath = item.chassisImageUrl;

            if (kDebugMode) {
              print('   Motor fingerprint path: $motorFingerprintPath');
              print('   Chassis path: $chassisPath');
              print('   Customer ID path: $customerIdImagePath');
            }

            // Create composite image with available images (local files or URLs)
            if (motorFingerprintPath.isNotEmpty && customerIdImagePath.isNotEmpty) {
              try {
                final compositeService = CompositeImageService();
                final compositeImage = await compositeService.createCompositeImage(
                  invoice: invoice,
                  item: item,
                  motorFingerprintImagePath: motorFingerprintPath,
                  chassisImagePath: chassisPath.isNotEmpty ? chassisPath : '',
                  customerIdImagePath: customerIdImagePath,
                );

                compositeImagePath = compositeImage.path;

                if (kDebugMode) {
                  print('✅ Composite image created for document tracking: $compositeImagePath');
                  print('   Motor fingerprint: $motorFingerprintPath');
                  print('   Customer ID: $customerIdImagePath');
                  print('   Chassis: $chassisPath');
                }
              } catch (e) {
                if (kDebugMode) {
                  print('❌ Failed to create composite image: $e');
                  print('   Motor fingerprint: $motorFingerprintPath');
                  print('   Customer ID: $customerIdImagePath');
                  print('   Chassis: $chassisPath');
                }
              }
            } else {
              if (kDebugMode) {
                print('⚠️ Cannot create composite image - missing required images:');
                print('   Motor fingerprint: $motorFingerprintPath');
                print('   Customer ID: $customerIdImagePath');
              }
            }
          } else {
            if (kDebugMode) {
              print('⚠️ No customer ID images found for composite image creation');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error creating composite image for document tracking: $e');
          }
          // Continue without composite image
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Cannot create composite image - missing invoice or item:');
          print('   Invoice: ${invoice?.id ?? 'null'}');
          print('   Item: ${item?.id ?? 'null'}');
        }
      }

      // Create initial status history with composite image info
      final initialStatus = DocumentStatusHistory(
        status: AppConstants.documentSentToManager,
        timestamp: now,
        updatedBy: createdBy,
        notes: compositeImagePath != null
            ? 'تم إنشاء تتبع الجواب عند بيع الموتور - تم إرسال البيانات للمدير مع الصورة المجمعة'
            : 'تم إنشاء تتبع الجواب عند بيع الموتور - تم إرسال البيانات للمدير',
      );

      final documentTracking = DocumentTrackingModel(
        id: trackingId,
        itemId: itemId,
        invoiceId: invoiceId,
        currentStatus: AppConstants.documentSentToManager,
        statusHistory: [initialStatus],
        createdAt: now,
        updatedAt: now,
        createdBy: createdBy,
        compositeImagePath: compositeImagePath, // Add composite image path
      );

      // Save to local database
      await _localDb.insert('document_tracking', documentTracking.toMap());

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.documentTrackingCollection)
            .doc(trackingId)
            .set(documentTracking.toFirestore());

        await _localDb.markAsSynced('document_tracking', trackingId);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('document_tracking', trackingId, 'INSERT', documentTracking.toMap());
      }

      if (kDebugMode) {
        print('✅ Document tracking created for item: $itemId');
        print('   Tracking ID: $trackingId');
        print('   Composite image path: ${compositeImagePath ?? 'null'}');
        if (compositeImagePath != null) {
          print('✅ Composite image attached: $compositeImagePath');
        } else {
          print('⚠️ No composite image created for this document tracking');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating document tracking: $e');
      }
      // Don't rethrow as this shouldn't block invoice creation
    }
  }

  // Get all document tracking records with force refresh option
  Future<List<DocumentTrackingModel>> getAllDocumentTracking({bool forceFromFirebase = false}) async {
    try {
      List<DocumentTrackingModel> documents = [];

      // ALWAYS try to get from Firebase if online (don't rely on local cache)
      if (await _isOnline()) {
        try {
          final snapshot = await _firebaseService.firestore
              .collection(AppConstants.documentTrackingCollection)
              .orderBy('createdAt', descending: true)
              .get();

          documents = [];
          for (final doc in snapshot.docs) {
            try {
              final document = DocumentTrackingModel.fromFirestore(doc);
              documents.add(document);
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing document tracking ${doc.id}: $e');
              }
              // Skip this document and continue
            }
          }

          // ALWAYS clear and update local database with Firebase data (even if empty)
          await _localDb.delete('document_tracking', '1=1', []);

          if (documents.isNotEmpty) {
            for (final document in documents) {
              await _localDb.insert('document_tracking', document.toMap());
            }
          }

          _updateLastSyncTime('document_tracking');

          if (kDebugMode) {
            print('✅ Synced ${documents.length} document tracking records from Firebase (cleared local completely and updated)');
          }

          return documents; // Return Firebase data directly
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error fetching document tracking from Firebase: $e');
          }
          // Clear local data if Firebase fails to avoid showing stale data
          await _localDb.delete('document_tracking', '1=1', []);
          return []; // Return empty list if Firebase fails
        }
      }

      // Only use local data if completely offline
      final localData = await _localDb.query(
        'document_tracking',
        orderBy: 'id DESC',
      );

      documents = localData
          .map((data) => DocumentTrackingModel.fromMap(data))
          .toList();

      if (kDebugMode) {
        print('📱 Loaded ${documents.length} document tracking records from local database (offline mode)');
      }

      return documents;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all document tracking: $e');
      }
      return [];
    }
  }

  /// Clear local document tracking data
  Future<void> clearLocalDocumentTracking() async {
    try {
      await _localDb.delete('document_tracking', '1=1', []);
      if (kDebugMode) {
        print('🧹 Cleared all local document tracking data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing local document tracking data: $e');
      }
    }
  }

  // Get document tracking by item ID
  Future<DocumentTrackingModel?> getDocumentTrackingByItemId(String itemId) async {
    try {
      // Try local database first
      final localDocs = await _localDb.query(
        'document_tracking',
        where: 'itemId = ?',
        whereArgs: [itemId]
      );

      if (localDocs.isNotEmpty) {
        return DocumentTrackingModel.fromMap(localDocs.first);
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final querySnapshot = await _firebaseService.firestore
            .collection(AppConstants.documentTrackingCollection)
            .where('itemId', isEqualTo: itemId)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final doc = DocumentTrackingModel.fromFirestore(querySnapshot.docs.first);
          await _localDb.insert('document_tracking', doc.toMap());
          return doc;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting document tracking by item ID: $e');
      }
      return null;
    }
  }

  // ==================== USER MANAGEMENT ====================

  /// Get users by role
  Future<List<UserModel>> getUsersByRole(String role) async {
    try {
      List<UserModel> users = [];

      // Get from local database
      final localUsers = await _localDb.query(
        'users',
        where: 'role = ?',
        whereArgs: [role]
      );

      // Use safe conversion for each user
      for (final userData in localUsers) {
        try {
          users.add(UserModel.fromMap(userData));
        } catch (e) {
          if (kDebugMode) {
            print('Error converting user data: $e');
          }
          // Skip this user and continue
        }
      }

      // If online, sync with Firebase
      if (await _isOnline()) {
        try {
          final querySnapshot = await _firebaseService.firestore
              .collection(AppConstants.usersCollection)
              .where('role', isEqualTo: role)
              .get();

          for (final doc in querySnapshot.docs) {
            try {
              final user = UserModel.fromFirestore(doc);

              // Check if already exists locally
              final existingIndex = users.indexWhere((u) => u.id == user.id);
              if (existingIndex != -1) {
                // Update existing
                users[existingIndex] = user;
              } else {
                // Add new
                users.add(user);
              }

              // Cache locally
              await _localDb.insert('users', user.toMap());
              await _localDb.markAsSynced('users', user.id);
            } catch (e) {
              if (kDebugMode) {
                print('Error processing user from Firebase: $e');
              }
              // Skip this user and continue
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error fetching users from Firebase: $e');
          }
          // Continue with local data only
        }
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting users by role: $e');
      }
      return [];
    }
  }

  // ==================== AGENT MANAGEMENT ====================

  /// Create or update agent account
  Future<void> createOrUpdateAgentAccount(AgentAccountModel account) async {
    try {
      // Save to local database
      await _localDb.insert('agent_accounts', account.toMap());

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.agentAccountsCollection)
            .doc(account.id)
            .set(account.toFirestore());

        await _localDb.markAsSynced('agent_accounts', account.id);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('agent_accounts', account.id, 'INSERT', account.toMap());
      }

      if (kDebugMode) {
        print('Agent account created/updated: ${account.agentName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating/updating agent account: $e');
      }
      rethrow;
    }
  }



  /// Get agent account by agent ID
  Future<AgentAccountModel?> getAgentAccount(String agentId) async {
    try {
      // Try local database first
      final localAccounts = await _localDb.query(
        'agent_accounts',
        where: 'agentId = ?',
        whereArgs: [agentId]
      );

      if (localAccounts.isNotEmpty) {
        return AgentAccountModel.fromMap(localAccounts.first);
      }

      // If not found locally and online, try Firebase
      if (await _isOnline()) {
        final querySnapshot = await _firebaseService.firestore
            .collection(AppConstants.agentAccountsCollection)
            .where('agentId', isEqualTo: agentId)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final account = AgentAccountModel.fromFirestore(querySnapshot.docs.first);

          // Cache locally
          await _localDb.insert('agent_accounts', account.toMap());
          await _localDb.markAsSynced('agent_accounts', account.id);

          return account;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agent account: $e');
      }
      return null;
    }
  }

  /// Get all agent accounts
  Future<List<AgentAccountModel>> getAllAgentAccounts() async {
    try {
      List<AgentAccountModel> accounts = [];

      // Get from local database
      final localAccounts = await _localDb.query('agent_accounts');
      accounts.addAll(localAccounts.map((map) => AgentAccountModel.fromMap(map)));

      // If online, sync with Firebase
      if (await _isOnline()) {
        final querySnapshot = await _firebaseService.firestore
            .collection(AppConstants.agentAccountsCollection)
            .get();

        for (final doc in querySnapshot.docs) {
          final account = AgentAccountModel.fromFirestore(doc);

          // Check if already exists locally
          final existingIndex = accounts.indexWhere((a) => a.id == account.id);
          if (existingIndex != -1) {
            // Update existing
            accounts[existingIndex] = account;
          } else {
            // Add new
            accounts.add(account);
          }

          // Cache locally
          await _localDb.insert('agent_accounts', account.toMap());
          await _localDb.markAsSynced('agent_accounts', account.id);
        }
      }

      return accounts;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all agent accounts: $e');
      }
      return [];
    }
  }

  /// Add transaction to agent account
  Future<void> addAgentTransaction(String agentId, AgentTransaction transaction) async {
    try {
      if (kDebugMode) {
        print('Adding transaction to agent $agentId: ${transaction.type} - ${transaction.amount}');
      }
      // Get or create agent account
      AgentAccountModel? account = await getAgentAccount(agentId);
      if (account == null) {
        // Create agent account if it doesn't exist
        final agent = await getUserById(agentId);
        if (agent == null) {
          throw Exception('Agent user not found');
        }

        account = AgentAccountModel(
          id: 'agent_${agentId}_${DateTime.now().millisecondsSinceEpoch}',
          agentId: agentId,
          agentName: agent.fullName,
          agentPhone: agent.phone,
          totalDebt: 0.0,
          totalPaid: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: _authService.currentUser?.id ?? 'system',
        );

        await createOrUpdateAgentAccount(account);

        if (kDebugMode) {
          print('Created new agent account for: ${agent.fullName}');
        }
      }

      // Calculate new balances
      double newTotalDebt = account.totalDebt;
      double newTotalPaid = account.totalPaid;

      if (transaction.type == 'debt') {
        newTotalDebt += transaction.amount;
      } else if (transaction.type == 'payment') {
        newTotalPaid += transaction.amount;
      }

      final newBalance = newTotalDebt - newTotalPaid;

      // Update account with new transaction
      final updatedTransactions = List<AgentTransaction>.from(account.transactions);
      updatedTransactions.add(transaction);

      if (kDebugMode) {
        print('📊 Before update: ${account.transactions.length} transactions');
        print('📊 After adding: ${updatedTransactions.length} transactions');
        print('📊 New transaction: ${transaction.type} - ${transaction.amount}');
      }

      final updatedAccount = account.copyWith(
        totalDebt: newTotalDebt,
        totalPaid: newTotalPaid,
        currentBalance: newBalance,
        transactions: updatedTransactions,
        updatedAt: DateTime.now(),
      );

      await createOrUpdateAgentAccount(updatedAccount);

      if (kDebugMode) {
        print('Transaction added to agent ${account.agentName}: ${transaction.type} ${transaction.amount}');
        print('📊 Updated account has ${updatedAccount.transactions.length} transactions');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding agent transaction: $e');
      }
      rethrow;
    }
  }

  /// Create agent transfer invoice
  Future<String> createAgentTransferInvoice(AgentTransferInvoiceModel invoice) async {
    try {
      final invoiceId = invoice.id;

      // Save to local database
      await _localDb.insert('agent_transfer_invoices', invoice.toMap());

      // Create debt transaction for agent
      final debtTransaction = AgentTransaction(
        id: '${DateTime.now().millisecondsSinceEpoch}_debt',
        type: 'debt',
        amount: invoice.totalAgentPrice,
        description: 'تحويل بضاعة - فاتورة ${invoice.invoiceNumber}',
        invoiceId: invoiceId,
        timestamp: DateTime.now(),
        createdBy: invoice.createdBy,
      );

      await addAgentTransaction(invoice.agentId, debtTransaction);

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.agentTransferInvoicesCollection)
            .doc(invoiceId)
            .set(invoice.toFirestore());

        await _localDb.markAsSynced('agent_transfer_invoices', invoiceId);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('agent_transfer_invoices', invoiceId, 'INSERT', invoice.toMap());
      }

      if (kDebugMode) {
        print('Agent transfer invoice created: ${invoice.invoiceNumber}');
      }

      return invoiceId;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating agent transfer invoice: $e');
      }
      rethrow;
    }
  }

  // ==================== ADVANCED WAREHOUSE MANAGEMENT ====================

  /// Record inventory movement
  Future<void> recordInventoryMovement(InventoryMovementModel movement) async {
    try {
      // Save to local database
      await _localDb.insert('inventory_movements', movement.toMap());

      // Update warehouse stock levels
      await _updateWarehouseStock(movement);

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection('inventory_movements')
            .doc(movement.id)
            .set(movement.toFirestore());

        await _localDb.markAsSynced('inventory_movements', movement.id);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('inventory_movements', movement.id, 'INSERT', movement.toMap());
      }

      if (kDebugMode) {
        print('✅ Inventory movement recorded successfully:');
        print('   - ID: ${movement.id}');
        print('   - Type: ${movement.movementType}');
        print('   - Item: ${movement.itemName}');
        print('   - Quantity: ${movement.quantity}');
        print('   - Source: ${movement.sourceWarehouseName}');
        print('   - Target: ${movement.targetWarehouseName ?? 'N/A'}');
        print('   - Saved to local DB and ${await _isOnline() ? 'Firebase' : 'sync queue'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error recording inventory movement: $e');
      }
      rethrow;
    }
  }

  /// Update warehouse stock levels based on movement
  Future<void> _updateWarehouseStock(InventoryMovementModel movement) async {
    try {
      // Update source warehouse (outbound movement)
      if (movement.isOutbound || movement.isTransfer) {
        await _adjustWarehouseStock(
          movement.sourceWarehouseId,
          movement.itemId,
          -movement.quantity,
        );
      }

      // Update target warehouse (inbound movement or transfer destination)
      if (movement.isInbound) {
        await _adjustWarehouseStock(
          movement.sourceWarehouseId,
          movement.itemId,
          movement.quantity,
        );
      } else if (movement.isTransfer && movement.targetWarehouseId != null) {
        await _adjustWarehouseStock(
          movement.targetWarehouseId!,
          movement.itemId,
          movement.quantity,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating warehouse stock: $e');
      }
      rethrow;
    }
  }

  /// Adjust warehouse stock for specific item
  Future<void> _adjustWarehouseStock(String warehouseId, String itemId, int quantityChange) async {
    try {
      // Get current stock
      final currentStock = await _getWarehouseItemStock(warehouseId, itemId);
      final newStock = currentStock + quantityChange;

      // Update stock in warehouse_items table
      await _localDb.update(
        'warehouse_items',
        {'quantity': newStock, 'lastUpdated': DateTime.now().toIso8601String()},
        'warehouseId = ? AND itemId = ?',
        [warehouseId, itemId],
      );

      if (kDebugMode) {
        print('Stock adjusted for warehouse $warehouseId, item $itemId: $currentStock -> $newStock');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adjusting warehouse stock: $e');
      }
      rethrow;
    }
  }

  /// Get current stock for item in warehouse
  Future<int> _getWarehouseItemStock(String warehouseId, String itemId) async {
    try {
      final result = await _localDb.query(
        'warehouse_items',
        where: 'warehouseId = ? AND itemId = ?',
        whereArgs: [warehouseId, itemId],
      );

      if (result.isNotEmpty) {
        return result.first['quantity'] ?? 0;
      }

      return 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse item stock: $e');
      }
      return 0;
    }
  }

  /// Get inventory movements for a warehouse
  Future<List<InventoryMovementModel>> getWarehouseMovements(String warehouseId, {int? limit}) async {
    try {
      String query = 'sourceWarehouseId = ? OR targetWarehouseId = ?';
      List<dynamic> args = [warehouseId, warehouseId];

      if (limit != null) {
        query += ' ORDER BY timestamp DESC LIMIT ?';
        args.add(limit);
      } else {
        query += ' ORDER BY timestamp DESC';
      }

      final movements = await _localDb.query(
        'inventory_movements',
        where: query,
        whereArgs: args,
      );

      return movements.map((map) => InventoryMovementModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse movements: $e');
      }
      return [];
    }
  }

  /// Get stock levels for all items in a warehouse
  Future<List<WarehouseStockModel>> getWarehouseStock(String warehouseId) async {
    try {
      final stockItems = await _localDb.query(
        'warehouse_items',
        where: 'warehouseId = ?',
        whereArgs: [warehouseId],
      );

      List<WarehouseStockModel> stockList = [];

      for (final stockItem in stockItems) {
        // Get item details
        final itemDetails = await _localDb.query(
          'items',
          where: 'id = ?',
          whereArgs: [stockItem['itemId']],
        );

        if (itemDetails.isNotEmpty) {
          final item = itemDetails.first;
          final warehouse = await _getWarehouseById(warehouseId);

          stockList.add(WarehouseStockModel(
            warehouseId: warehouseId,
            warehouseName: warehouse?.name ?? 'مخزن غير معروف',
            warehouseType: warehouse?.type ?? 'unknown',
            itemId: stockItem['itemId'],
            itemName: item['name'] ?? '',
            brand: item['brand'] ?? '',
            model: item['model'] ?? '',
            currentStock: stockItem['quantity'] ?? 0,
            minStock: stockItem['minStock'] ?? 0,
            maxStock: stockItem['maxStock'] ?? 100,
            averageCost: stockItem['averageCost']?.toDouble(),
            lastUpdated: DateTime.parse(stockItem['lastUpdated'] ?? DateTime.now().toIso8601String()),
            motorFingerprintText: item['motorFingerprintText'],
          ));
        }
      }

      return stockList;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse stock: $e');
      }
      return [];
    }
  }

  /// Transfer a single item between warehouses with enhanced validation
  Future<void> transferItemBetweenWarehouses({
    required String sourceWarehouseId,
    required String targetWarehouseId,
    required String itemId,
    required String createdBy,
    String? notes,
  }) async {
    try {
      // Get item details first
      final item = await getItemById(itemId);
      if (item == null) {
        throw Exception('الصنف غير موجود');
      }

      // Get warehouse details for validation
      final sourceWarehouse = await getWarehouseById(sourceWarehouseId);
      final targetWarehouse = await getWarehouseById(targetWarehouseId);

      if (sourceWarehouse == null) {
        throw Exception('المخزن المصدر غير موجود');
      }

      if (targetWarehouse == null) {
        throw Exception('المخزن المستهدف غير موجود');
      }

      // Verify item is in source warehouse
      if (item.currentWarehouseId != sourceWarehouseId) {
        throw Exception('الصنف غير موجود في المخزن المصدر');
      }

      // Verify item is available for transfer
      if (item.status == 'مباع' || item.status == 'محجوز') {
        throw Exception('لا يمكن تحويل صنف ${item.status}');
      }

      // Validate transfer rules based on warehouse types
      await _validateTransferRules(sourceWarehouse, targetWarehouse, item);

      // Create goods invoice for agent if transferring to agent warehouse
      if (kDebugMode) {
        print('Transfer check: isAgentWarehouse=${targetWarehouse.isAgentWarehouse}, ownerId=${targetWarehouse.ownerId}');
      }

      if (targetWarehouse.isAgentWarehouse && targetWarehouse.ownerId != null && targetWarehouse.ownerId!.isNotEmpty) {
        if (kDebugMode) {
          print('Creating goods invoice for agent: ${targetWarehouse.ownerId}');
        }
        await _createGoodsInvoiceForAgent(
          agentId: targetWarehouse.ownerId!,
          item: item,
          createdBy: createdBy,
        );
      } else {
        if (kDebugMode) {
          print('Not creating goods invoice - not agent warehouse or no owner');
        }
      }

      // Update item in local database first
      await _localDb.update(
        'items',
        {
          'currentWarehouseId': targetWarehouseId,
          'status': 'متاح', // Keep as available in new warehouse
          'updatedAt': DateTime.now().toIso8601String(),
        },
        'id = ?',
        [itemId],
      );

      // Create transfer record in local database
      final transferId = AppUtils.generateId();
      await _localDb.insert('transfers', {
        'id': transferId,
        'sourceWarehouseId': sourceWarehouseId,
        'targetWarehouseId': targetWarehouseId,
        'itemId': itemId,
        'quantity': 1,
        'createdBy': createdBy,
        'notes': notes,
        'createdAt': DateTime.now().toIso8601String(),
      });

      // Create inventory movement record for reports
      final movement = InventoryMovementModel(
        id: '${DateTime.now().millisecondsSinceEpoch}_transfer_$itemId',
        itemId: itemId,
        itemName: '${item.brand} ${item.model}',
        brand: item.brand,
        model: item.model,
        movementType: 'transfer',
        quantity: 1,
        sourceWarehouseId: sourceWarehouseId,
        targetWarehouseId: targetWarehouseId,
        sourceWarehouseName: sourceWarehouse.name,
        targetWarehouseName: targetWarehouse.name,
        reason: 'transfer',
        referenceId: transferId,
        timestamp: DateTime.now(),
        createdBy: createdBy,
        notes: notes,
      );

      // Try to record inventory movement with error handling
      try {
        await recordInventoryMovement(movement);

        if (kDebugMode) {
          print('✅ Transfer inventory movement recorded:');
          print('   - Movement ID: ${movement.id}');
          print('   - Item: ${movement.itemName}');
          print('   - From: ${movement.sourceWarehouseName}');
          print('   - To: ${movement.targetWarehouseName}');
          print('   - Type: ${movement.movementType}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Failed to record inventory movement: $e');
          print('⚠️ Transfer completed but movement not recorded in database');
        }
        // Don't throw error - transfer was successful, just movement recording failed
      }

      // Update Firebase if online
      if (await _isOnline()) {
        try {
          final batch = FirebaseFirestore.instance.batch();

          // Update item's warehouse
          final itemRef = FirebaseFirestore.instance.collection('items').doc(itemId);
          batch.update(itemRef, {
            'currentWarehouseId': targetWarehouseId,
            'status': 'متاح',
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create transfer record
          final transferRef = FirebaseFirestore.instance.collection('transfers').doc(transferId);
          batch.set(transferRef, {
            'id': transferId,
            'sourceWarehouseId': sourceWarehouseId,
            'targetWarehouseId': targetWarehouseId,
            'itemId': itemId,
            'quantity': 1,
            'createdBy': createdBy,
            'notes': notes,
            'createdAt': FieldValue.serverTimestamp(),
          });

          await batch.commit();
        } catch (e) {
          if (kDebugMode) {
            print('Failed to sync transfer to Firebase: $e');
          }
          // Continue - local update was successful
        }
      }

      // Send notification to agent if transferring to agent warehouse
      if (targetWarehouse.isAgentWarehouse && targetWarehouse.ownerId != null && targetWarehouse.ownerId!.isNotEmpty) {
        try {
          // Create a simple notification for the agent
          final notification = NotificationModel(
            id: AppUtils.generateId(),
            title: 'تحويل بضاعة جديدة',
            message: 'تم تحويل ${item.brand} ${item.model} إلى مخزنك\n'
                   'البصمة: ${item.motorFingerprintText}\n'
                   'من: ${sourceWarehouse.name}',
            type: 'item_transfer',
            targetUserId: targetWarehouse.ownerId!,
            data: {
              'itemId': item.id,
              'itemBrand': item.brand,
              'itemModel': item.model,
              'motorFingerprint': item.motorFingerprintText,
              'fromWarehouse': sourceWarehouse.name,
              'toWarehouse': targetWarehouse.name,
              'transferDate': DateTime.now().toIso8601String(),
            },
            createdAt: DateTime.now(),
            createdBy: createdBy,
            isRead: false,
          );

          // Save notification to database
          await createNotification(notification);

          if (kDebugMode) {
            print('Transfer notification sent to agent: ${targetWarehouse.ownerId}');
          }
        } catch (e) {
          debugPrint('Failed to send transfer notification: $e');
        }
      }

      // Verify the transfer was successful
      final updatedItem = await getItemById(itemId);
      if (updatedItem != null) {
        if (kDebugMode) {
          print('✅ Transfer verified: Item $itemId is now in warehouse ${updatedItem.currentWarehouseId} with status ${updatedItem.status}');
        }
      } else {
        if (kDebugMode) {
          print('❌ Transfer verification failed: Item $itemId not found after transfer');
        }
      }

      if (kDebugMode) {
        print('Item $itemId transferred from $sourceWarehouseId to $targetWarehouseId');
      }
    } catch (e) {
      throw Exception('فشل في تحويل الصنف: $e');
    }
  }

  /// Create goods invoice for agent when transferring items
  Future<void> _createGoodsInvoiceForAgent({
    required String agentId,
    required ItemModel item,
    required String createdBy,
  }) async {
    try {
      if (kDebugMode) {
        print('Starting _createGoodsInvoiceForAgent for agent: $agentId, item: ${item.motorFingerprintText}');
      }
      // Create goods invoice
      final invoiceId = AppUtils.generateId();
      final invoice = InvoiceModel(
        id: invoiceId,
        invoiceNumber: 'GOODS-${DateTime.now().millisecondsSinceEpoch}',
        type: 'goods', // فاتورة بضاعة
        agentId: agentId,
        warehouseId: item.currentWarehouseId,
        itemId: item.id,
        itemCost: item.purchasePrice,
        sellingPrice: item.purchasePrice, // نفس سعر الشراء للوكيل
        profitAmount: 0, // لا يوجد ربح في فاتورة البضاعة
        companyProfitShare: 0,
        agentProfitShare: 0,
        status: 'confirmed', // مؤكدة
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: createdBy,
        additionalData: {
          'itemType': item.type,
          'itemModel': item.model,
          'itemBrand': item.brand,
          'itemColor': item.color,
          'transferType': 'goods_transfer',
        },
      );

      // Save invoice locally
      await _localDb.insert('invoices', invoice.toMap());

      // Update agent account balance
      final transaction = AgentTransaction(
        id: AppUtils.generateId(),
        type: 'debt',
        amount: item.purchasePrice,
        description: 'فاتورة بضاعة - ${item.type} ${item.model}',
        invoiceId: invoiceId,
        timestamp: DateTime.now(),
        createdBy: createdBy,
      );

      // Add transaction to agent account
      await addAgentTransaction(agentId, transaction);

      if (kDebugMode) {
        print('Agent account updated for transfer: $agentId, Amount: ${item.purchasePrice}');
      }

      // Send notification to receiving agent using enhanced notification service
      try {
        final notificationService = EnhancedNotificationService.instance;
        final transferredByUser = await getUserById(createdBy);

        await notificationService.sendAgentTransferNotification(
          fromAgentId: createdBy,
          fromAgentName: transferredByUser?.fullName ?? 'المؤسسة',
          toAgentId: agentId,
          toAgentName: 'الوكيل', // Will be updated with actual name in the service
          itemNames: ['${item.brand} ${item.model}'],
          transferId: invoiceId,
        );

        if (kDebugMode) {
          print('✅ Transfer notification sent to receiving agent $agentId');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Failed to send notification to agent: $e');
        }
      }

      // Sync to Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance.collection('invoices').doc(invoiceId).set({
            ...invoice.toMap(),
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          if (kDebugMode) {
            print('Failed to sync goods invoice to Firebase: $e');
          }
        }
      }

      if (kDebugMode) {
        print('Created goods invoice for agent $agentId: ${item.purchasePrice} EGP');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating goods invoice for agent: $e');
      }
      // Don't throw error - transfer should continue
    }
  }

  /// Get main warehouse
  Future<WarehouseModel?> getMainWarehouse() async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.firstWhere(
        (w) => w.isMainWarehouse,
        orElse: () => throw Exception('المخزن الرئيسي غير موجود'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting main warehouse: $e');
      }
      return null;
    }
  }

  /// Get showroom warehouse
  Future<WarehouseModel?> getShowroomWarehouse() async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.firstWhere(
        (w) => w.isShowroomWarehouse,
        orElse: () => throw Exception('مخزن المعرض غير موجود'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting showroom warehouse: $e');
      }
      return null;
    }
  }

  /// Get agent warehouses
  Future<List<WarehouseModel>> getAgentWarehouses() async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.where((w) => w.isAgentWarehouse).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agent warehouses: $e');
      }
      return [];
    }
  }

  /// Get warehouse by agent ID
  Future<WarehouseModel?> getWarehouseByAgentId(String agentId) async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.firstWhere(
        (w) => w.isAgentWarehouse && w.ownerId == agentId,
        orElse: () => throw Exception('مخزن الوكيل غير موجود'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse for agent $agentId: $e');
      }
      return null;
    }
  }

  /// Get warehouses by owner ID (for agents)
  Future<List<WarehouseModel>> getWarehousesByOwnerId(String ownerId) async {
    try {
      final warehouses = await getAllWarehouses();
      return warehouses.where((w) =>
        w.isAgentWarehouse && w.ownerId == ownerId).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouses for owner $ownerId: $e');
      }
      return [];
    }
  }

  /// Validate transfer rules based on warehouse types
  Future<void> _validateTransferRules(
    WarehouseModel sourceWarehouse,
    WarehouseModel targetWarehouse,
    ItemModel item,
  ) async {
    // Rule 1: Only main warehouse can be the source for new items
    if (item.status == 'جديد' && !sourceWarehouse.isMainWarehouse) {
      throw Exception('الأصناف الجديدة يجب أن تحول من المخزن الرئيسي فقط');
    }

    // Rule 2: Cannot transfer from main to main
    if (sourceWarehouse.isMainWarehouse && targetWarehouse.isMainWarehouse) {
      throw Exception('لا يمكن التحويل من المخزن الرئيسي إلى نفسه');
    }

    // Rule 3: Agent warehouses can only receive from main warehouse
    if (targetWarehouse.isAgentWarehouse && !sourceWarehouse.isMainWarehouse) {
      throw Exception('مخازن الوكلاء تستقبل البضاعة من المخزن الرئيسي فقط');
    }

    // Rule 4: Showroom warehouse can only receive from main warehouse
    if (targetWarehouse.isShowroomWarehouse && !sourceWarehouse.isMainWarehouse) {
      throw Exception('مخزن المعرض يستقبل البضاعة من المخزن الرئيسي فقط');
    }

    // Rule 5: Cannot transfer back to main warehouse once moved
    if (targetWarehouse.isMainWarehouse && !sourceWarehouse.isMainWarehouse) {
      throw Exception('لا يمكن إرجاع البضاعة للمخزن الرئيسي بعد تحويلها');
    }
  }



  /// Create user with password
  Future<String> createUserWithPassword(UserModel user, String password) async {
    try {
      // Create user in local database first
      final userId = AppUtils.generateId();

      // Add password to additionalData for local authentication
      final additionalData = Map<String, dynamic>.from(user.additionalData ?? {});
      additionalData['password'] = password; // In production, this should be hashed

      final userWithId = user.copyWith(
        id: userId,
        additionalData: additionalData,
      );

      await _localDb.createUser(userWithId.toMap());

      // Note: Agent warehouses should be created manually through Firebase Console
      // No automatic warehouse creation

      // Create agent account for agent users
      if (userWithId.role == 'agent') {
        await _createAgentAccount(userWithId);
      }

      // Try to create in Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance.collection('users').doc(userId).set({
            ...userWithId.toMap(),
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          debugPrint('Failed to sync user to Firebase: $e');
        }
      }

      return userId;
    } catch (e) {
      throw Exception('فشل في إنشاء المستخدم: $e');
    }
  }



  /// Create account for agent user
  Future<void> _createAgentAccount(UserModel agent) async {
    try {
      final account = AgentAccountModel(
        id: 'agent_${agent.id}_${DateTime.now().millisecondsSinceEpoch}',
        agentId: agent.id,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        totalDebt: 0.0,
        totalPaid: 0.0,
        currentBalance: 0.0,
        transactions: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: _authService.currentUser?.id ?? 'system',
      );

      await createOrUpdateAgentAccount(account);

      if (kDebugMode) {
        print('Created account for agent: ${agent.fullName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating agent account: $e');
      }
      // Don't throw error - user creation should continue
    }
  }

  /// Update warehouse manager
  Future<void> updateWarehouseManager(String warehouseId, String managerId) async {
    try {
      // Update in local database
      await _localDb.update(
        'warehouses',
        {
          'ownerId': managerId,
          'updatedAt': DateTime.now().toIso8601String(),
        },
        'id = ?',
        [warehouseId],
      );

      // Update in Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance.collection('warehouses').doc(warehouseId).update({
            'ownerId': managerId,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          debugPrint('Failed to sync warehouse update to Firebase: $e');
        }
      }

      if (kDebugMode) {
        print('Updated warehouse $warehouseId with ownerId: $managerId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating warehouse manager: $e');
      }
      throw Exception('فشل في تحديث مدير المخزن: $e');
    }
  }

  /// Clear demo data
  Future<void> clearDemoData() async {
    try {
      await _localDb.clearDemoData();
      debugPrint('Demo data cleared successfully');
    } catch (e) {
      throw Exception('فشل في مسح البيانات الوهمية: $e');
    }
  }

  /// Update document status with notifications
  Future<void> updateDocumentStatus(
    String documentId,
    String newStatus,
    String notes,
  ) async {
    try {
      // Get current document
      final currentDoc = await _localDb.query(
        'document_tracking',
        where: 'id = ?',
        whereArgs: [documentId],
      );

      if (currentDoc.isEmpty) {
        throw Exception('الوثيقة غير موجودة');
      }

      final oldStatus = currentDoc.first['status'] as String;
      final itemBrand = currentDoc.first['itemBrand'] as String;
      final itemModel = currentDoc.first['itemModel'] as String;
      final itemId = currentDoc.first['itemId'] as String;
      final agentId = currentDoc.first['agentId'] as String;

      // Update document status
      await _localDb.update(
        'document_tracking',
        {
          'status': newStatus,
          'notes': notes,
          'lastUpdated': DateTime.now().toIso8601String(),
        },
        'id = ?',
        [documentId],
      );

      // Send notification to agent if status changed
      if (oldStatus != newStatus) {
        try {
          // Send notification using enhanced notification service
          final notificationService = EnhancedNotificationService.instance;
          await notificationService.sendDocumentStatusUpdateNotification(
            itemId: itemId,
            itemDescription: '$itemBrand $itemModel',
            newStatus: newStatus,
            agentId: agentId,
          );

          if (kDebugMode) {
            print('✅ Document status notification sent: $itemBrand $itemModel from $oldStatus to $newStatus');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to send document status notification: $e');
          }
        }
      }

      // Sync to Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance
              .collection('document_tracking')
              .doc(documentId)
              .update({
            'status': newStatus,
            'notes': notes,
            'lastUpdated': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          debugPrint('Failed to sync document update to Firebase: $e');
        }
      }
    } catch (e) {
      throw Exception('فشل في تحديث حالة الوثيقة: $e');
    }
  }

  /// Get all documents (for managers)
  Future<List<Map<String, dynamic>>> getAllDocuments() async {
    try {
      return await _localDb.query(
        'document_tracking',
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في تحميل الوثائق: $e');
    }
  }

  /// Get agent documents
  Future<List<Map<String, dynamic>>> getAgentDocuments(String agentId) async {
    try {
      return await _localDb.query(
        'document_tracking',
        where: 'agentId = ?',
        whereArgs: [agentId],
        orderBy: 'createdAt DESC',
      );
    } catch (e) {
      throw Exception('فشل في تحميل وثائق الوكيل: $e');
    }
  }

  /// Notify managers about new invoice
  Future<void> _notifyManagersNewInvoice(InvoiceModel invoice) async {
    try {
      // Get all super admin users (managers)
      final managers = await _localDb.query(
        'users',
        where: 'role = ?',
        whereArgs: [AppConstants.superAdminRole],
      );

      // Get agent name
      String agentName = 'وكيل غير محدد';
      if (invoice.agentId != null) {
        final agentData = await _localDb.query(
          'users',
          where: 'id = ?',
          whereArgs: [invoice.agentId],
        );
        if (agentData.isNotEmpty) {
          agentName = agentData.first['fullName'] as String? ?? 'وكيل غير محدد';
        }
      }

      // Calculate total amount
      final totalAmount = invoice.sellingPrice;

      // Create notification for each manager
      for (final manager in managers) {
        final managerId = manager['id'] as String;

        // Create notification record
        await _localDb.insert('notifications', {
          'id': AppUtils.generateId(),
          'userId': managerId,
          'title': 'فاتورة جديدة',
          'message': 'تم إنشاء فاتورة جديدة رقم ${invoice.invoiceNumber} بواسطة $agentName بقيمة ${totalAmount.toStringAsFixed(2)} جنيه',
          'type': 'new_invoice',
          'data': {
            'invoice_id': invoice.id,
            'invoice_number': invoice.invoiceNumber,
            'agent_name': agentName,
            'total_amount': totalAmount.toString(),
          }.toString(),
          'isRead': 0,
          'createdAt': DateTime.now().toIso8601String(),
        });
      }

      // Use enhanced notification service
      final notificationService = EnhancedNotificationService.instance;

      // Get agent info
      UserModel? agent;
      if (invoice.agentId != null) {
        final agentData = await _localDb.query(
          'users',
          where: 'id = ?',
          whereArgs: [invoice.agentId],
        );
        if (agentData.isNotEmpty) {
          agent = UserModel.fromMap(agentData.first);
        }
      }

      if (agent != null) {
        await notificationService.sendInvoiceCreatedNotification(
          invoice: invoice,
          agent: agent,
        );
      }

      if (kDebugMode) {
        print('✅ Invoice notification sent for ${invoice.invoiceNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to notify managers about new invoice: $e');
      }
      // Don't throw error as this shouldn't block invoice creation
    }
  }

  /// Get agent invoices
  Future<List<InvoiceModel>> getAgentInvoices(String agentId) async {
    try {
      List<Map<String, dynamic>> invoiceData;

      if (agentId.isEmpty) {
        // Get all invoices for admins
        invoiceData = await _localDb.query(
          'invoices',
          orderBy: 'createdAt DESC',
        );
      } else {
        // Get specific agent invoices
        invoiceData = await _localDb.query(
          'invoices',
          where: 'agentId = ?',
          whereArgs: [agentId],
          orderBy: 'createdAt DESC',
        );
      }

      return invoiceData.map((data) => InvoiceModel.fromMap(data)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل فواتير الوكيل: $e');
    }
  }

  /// Get agent payments with Firebase sync
  Future<List<Map<String, dynamic>>> getAgentPayments(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔍 Getting agent payments for agentId: $agentId');
      }

      // Try to sync from Firebase first if online
      if (await _isOnline()) {
        try {
          final snapshot = await FirebaseFirestore.instance
              .collection('agent_payments')
              .where('agentId', isEqualTo: agentId)
              .get();

          if (snapshot.docs.isNotEmpty) {
            // Clear local payments for this agent and update with Firebase data
            await _localDb.delete('agent_payments', 'agentId = ?', [agentId]);

            for (final doc in snapshot.docs) {
              final data = Map<String, dynamic>.from(doc.data());
              data['id'] = doc.id;

              // Convert Firestore Timestamps to ISO strings
              if (data['createdAt'] is Timestamp) {
                data['createdAt'] = (data['createdAt'] as Timestamp).toDate().toIso8601String();
              }
              if (data['updatedAt'] is Timestamp) {
                data['updatedAt'] = (data['updatedAt'] as Timestamp).toDate().toIso8601String();
              }
              if (data['paymentDate'] is Timestamp) {
                data['paymentDate'] = (data['paymentDate'] as Timestamp).toDate().toIso8601String();
              }

              // Ensure all required fields are present
              data['receiptNumber'] = data['receiptNumber'] ?? doc.id;
              data['paymentMethod'] = data['paymentMethod'] ?? 'cash';
              data['paymentDate'] = data['paymentDate'] ?? data['createdAt'];
              data['status'] = data['status'] ?? 'confirmed';
              data['updatedAt'] = data['updatedAt'] ?? data['createdAt'];

              await _localDb.insert('agent_payments', data);
            }

            if (kDebugMode) {
              print('✅ Synced ${snapshot.docs.length} payments from Firebase for agent $agentId');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to sync payments from Firebase: $e');
          }
        }
      }

      // Get payments from local database
      final payments = await _localDb.query(
        'agent_payments',
        where: 'agentId = ?',
        whereArgs: [agentId],
        orderBy: 'createdAt DESC',
      );

      if (kDebugMode) {
        print('📊 Found ${payments.length} payments for agent $agentId');
        if (payments.isNotEmpty) {
          print('💰 Sample payment: ${payments.first}');
        }
      }

      return payments;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent payments: $e');
      }
      throw Exception('فشل في تحميل مدفوعات الوكيل: $e');
    }
  }

  /// Get all agent payments (for managers) with Firebase sync
  Future<List<Map<String, dynamic>>> getAllAgentPayments() async {
    try {
      if (kDebugMode) {
        print('🔍 Getting all agent payments');
      }

      // Try to sync from Firebase first if online
      if (await _isOnline()) {
        try {
          final snapshot = await FirebaseFirestore.instance
              .collection('agent_payments')
              .get();

          if (snapshot.docs.isNotEmpty) {
            // Clear all local payments and update with Firebase data
            await _localDb.delete('agent_payments', '1=1', []);

            for (final doc in snapshot.docs) {
              final data = Map<String, dynamic>.from(doc.data());
              data['id'] = doc.id;

              // Convert Firestore Timestamps to ISO strings
              if (data['createdAt'] is Timestamp) {
                data['createdAt'] = (data['createdAt'] as Timestamp).toDate().toIso8601String();
              }
              if (data['updatedAt'] is Timestamp) {
                data['updatedAt'] = (data['updatedAt'] as Timestamp).toDate().toIso8601String();
              }
              if (data['paymentDate'] is Timestamp) {
                data['paymentDate'] = (data['paymentDate'] as Timestamp).toDate().toIso8601String();
              }

              // Ensure all required fields are present
              data['receiptNumber'] = data['receiptNumber'] ?? doc.id;
              data['paymentMethod'] = data['paymentMethod'] ?? 'cash';
              data['paymentDate'] = data['paymentDate'] ?? data['createdAt'];
              data['status'] = data['status'] ?? 'confirmed';
              data['updatedAt'] = data['updatedAt'] ?? data['createdAt'];

              await _localDb.insert('agent_payments', data);
            }

            if (kDebugMode) {
              print('✅ Synced ${snapshot.docs.length} payments from Firebase');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to sync all payments from Firebase: $e');
          }
        }
      }

      // Get payments from local database
      final payments = await _localDb.query(
        'agent_payments',
        orderBy: 'createdAt DESC',
      );

      if (kDebugMode) {
        print('📊 Found ${payments.length} total payments');
      }

      return payments;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all agent payments: $e');
      }
      throw Exception('فشل في تحميل جميع مدفوعات الوكلاء: $e');
    }
  }

  /// Create notification
  Future<void> createNotification(NotificationModel notification) async {
    try {
      await _localDb.insert('notifications', notification.toMap());

      if (kDebugMode) {
        print('✅ Notification created: ${notification.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
      throw Exception('فشل في إنشاء الإشعار: $e');
    }
  }

  /// Get user notifications
  Future<List<NotificationModel>> getUserNotifications(String userId, {
    String? userRole,
    bool unreadOnly = false,
    int limit = 50,
  }) async {
    try {
      String whereClause = '(targetUserId = ? OR targetUserId IS NULL)';
      List<dynamic> whereArgs = [userId];

      if (userRole != null) {
        whereClause += ' AND (targetRole = ? OR targetRole IS NULL)';
        whereArgs.add(userRole);
      }

      if (unreadOnly) {
        whereClause += ' AND isRead = 0';
      }

      final results = await _localDb.query(
        'notifications',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'createdAt DESC',
        limit: limit,
      );

      return results.map((map) => NotificationModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user notifications: $e');
      }
      throw Exception('فشل في تحميل الإشعارات: $e');
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _localDb.update(
        'notifications',
        {'isRead': 1, 'readAt': DateTime.now().toIso8601String()},
        'id = ?',
        [notificationId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      throw Exception('فشل في تحديث حالة الإشعار: $e');
    }
  }

  /// Mark all notifications as read for user
  Future<void> markAllNotificationsAsRead(String userId, {String? userRole}) async {
    try {
      String whereClause = '(targetUserId = ? OR targetUserId IS NULL) AND isRead = 0';
      List<dynamic> whereArgs = [userId];

      if (userRole != null) {
        whereClause += ' AND (targetRole = ? OR targetRole IS NULL)';
        whereArgs.add(userRole);
      }

      await _localDb.update(
        'notifications',
        {'isRead': 1, 'readAt': DateTime.now().toIso8601String()},
        whereClause,
        whereArgs,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
      throw Exception('فشل في تحديث حالة جميع الإشعارات: $e');
    }
  }

  /// Get unread notifications count
  Future<int> getUnreadNotificationsCount(String userId, {String? userRole}) async {
    try {
      String whereClause = '(targetUserId = ? OR targetUserId IS NULL) AND isRead = 0';
      List<dynamic> whereArgs = [userId];

      if (userRole != null) {
        whereClause += ' AND (targetRole = ? OR targetRole IS NULL)';
        whereArgs.add(userRole);
      }

      final result = await _localDb.query(
        'notifications',
        where: whereClause,
        whereArgs: whereArgs,
      );

      return result.length;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unread notifications count: $e');
      }
      return 0;
    }
  }

  /// Add agent payment (only managers can record payments)
  Future<void> addAgentPayment({
    required String agentId,
    required double amount,
    String? notes,
  }) async {
    try {
      // Check permissions - only managers can record agent payments
      await _validateAgentPaymentPermissions(agentId);

      // Validate payment data
      await _validatePaymentData(agentId, amount);

      final currentUser = _authService.currentUser!;
      final paymentId = AppUtils.generateId();
      final now = DateTime.now();

      // Create payment transaction for agent account
      final paymentTransaction = AgentTransaction(
        id: AppUtils.generateId(),
        type: 'payment',
        amount: amount,
        description: 'دفعة نقدية - ${notes ?? 'دفعة من الوكيل'}',
        timestamp: now,
        createdBy: currentUser.id,
        metadata: {
          'paymentId': paymentId,
          'notes': notes,
        },
      );

      // Add transaction to agent account
      await addAgentTransaction(agentId, paymentTransaction);

      // Record payment in payments table with all required fields
      final paymentData = {
        'id': paymentId,
        'receiptNumber': paymentId,
        'agentId': agentId,
        'amount': amount,
        'paymentMethod': 'cash',
        'paymentDate': now.toIso8601String(),
        'notes': notes ?? '',
        'createdAt': now.toIso8601String(),
        'updatedAt': now.toIso8601String(),
        'createdBy': currentUser.id,
        'status': 'confirmed',
      };

      if (kDebugMode) {
        print('💰 Recording agent payment: $paymentData');
      }

      await _localDb.insert('agent_payments', paymentData);

      if (kDebugMode) {
        print('✅ Agent payment recorded successfully: $paymentId');
      }

      // Sync to Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance
              .collection('agent_payments')
              .doc(paymentId)
              .set({
            'id': paymentId,
            'receiptNumber': paymentId,
            'agentId': agentId,
            'amount': amount,
            'paymentMethod': 'cash',
            'paymentDate': now.toIso8601String(),
            'notes': notes ?? '',
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
            'createdBy': currentUser.id,
            'status': 'confirmed',
          });

          if (kDebugMode) {
            print('✅ Payment synced to Firebase: $paymentId');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to sync payment to Firebase: $e');
          }
        }
      }

      // Send notification about payment received
      try {
        // Get agent info
        final agentData = await _localDb.query(
          'users',
          where: 'id = ?',
          whereArgs: [agentId],
        );

        if (agentData.isNotEmpty) {
          final agent = UserModel.fromMap(agentData.first);

          // Use enhanced notification service to notify the agent
          final notificationService = EnhancedNotificationService.instance;
          await notificationService.sendPaymentRecordedNotification(
            agentId: agentId,
            agentName: agent.fullName,
            amount: amount,
            paymentMethod: 'نقدي',
            recordedBy: currentUser.fullName,
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Failed to send payment notification: $e');
        }
        // Don't throw error as this shouldn't block payment recording
      }

      if (kDebugMode) {
        print('Agent payment recorded: $paymentId for agent $agentId, amount: $amount');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error adding agent payment: $e');
      }
      rethrow;
    }
  }

  /// Validate agent payment permissions
  Future<void> _validateAgentPaymentPermissions(String agentId) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // Only super admin and admin can record agent payments
      if (currentUser.role != AppConstants.superAdminRole &&
          currentUser.role != AppConstants.adminRole) {
        throw 'فقط المديرون يمكنهم تسجيل دفعات الوكلاء';
      }

      // Prevent agents from recording payments for themselves
      if (currentUser.role == AppConstants.agentRole && currentUser.id == agentId) {
        throw 'لا يمكن للوكيل تسجيل دفعة لنفسه';
      }

      // Verify agent exists
      final agent = await getUserById(agentId);
      if (agent == null) {
        throw 'الوكيل غير موجود';
      }

      if (agent.role != AppConstants.agentRole) {
        throw 'المستخدم المحدد ليس وكيلاً';
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error validating agent payment permissions: $e');
      }
      rethrow;
    }
  }

  /// Validate payment data
  Future<void> _validatePaymentData(String agentId, double amount) async {
    try {
      if (amount <= 0) {
        throw 'مبلغ الدفعة يجب أن يكون أكبر من صفر';
      }

      if (amount > 1000000) { // Maximum payment limit
        throw 'مبلغ الدفعة كبير جداً (الحد الأقصى: 1,000,000 جنيه)';
      }

      // Check if agent has debt to pay
      final agentAccount = await getAgentAccount(agentId);
      if (agentAccount != null && agentAccount.currentBalance <= 0) {
        // Agent has no debt or has credit
        if (agentAccount.currentBalance < 0) {
          throw 'الوكيل لديه رصيد دائن قدره ${AppUtils.formatCurrency(agentAccount.currentBalance.abs())}';
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error validating payment data: $e');
      }
      rethrow;
    }
  }

  /// Get agent account summary with recent transactions
  Future<Map<String, dynamic>> getAgentAccountSummary(String agentId) async {
    try {
      final account = await getAgentAccount(agentId);
      if (account == null) {
        return {
          'hasAccount': false,
          'totalDebt': 0.0,
          'totalPaid': 0.0,
          'currentBalance': 0.0,
          'recentTransactions': <AgentTransaction>[],
        };
      }

      return {
        'hasAccount': true,
        'totalDebt': account.totalDebt,
        'totalPaid': account.totalPaid,
        'currentBalance': account.currentBalance,
        'recentTransactions': account.getRecentTransactions(limit: 5),
        'lastUpdated': account.updatedAt,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agent account summary: $e');
      }
      rethrow;
    }
  }

  /// Check if current user can record payments for specific agent
  bool canRecordAgentPayment(String agentId) {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return false;

      // Only super admin and admin can record payments
      if (currentUser.role != AppConstants.superAdminRole &&
          currentUser.role != AppConstants.adminRole) {
        return false;
      }

      // Prevent agents from recording payments for themselves
      if (currentUser.role == AppConstants.agentRole && currentUser.id == agentId) {
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking payment recording permission: $e');
      }
      return false;
    }
  }

  /// Get payment history for agent
  Future<List<Map<String, dynamic>>> getAgentPaymentHistory(String agentId) async {
    try {
      // Check permissions
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // Agents can only view their own payment history
      if (currentUser.role == AppConstants.agentRole && currentUser.id != agentId) {
        throw 'لا يمكنك عرض تاريخ دفعات وكيل آخر';
      }

      // Managers can view any agent's payment history
      if (currentUser.role != AppConstants.superAdminRole &&
          currentUser.role != AppConstants.adminRole &&
          currentUser.id != agentId) {
        throw 'ليس لديك صلاحية لعرض تاريخ الدفعات';
      }

      return await getAgentPayments(agentId);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agent payment history: $e');
      }
      rethrow;
    }
  }

  /// Get current company poster URL
  Future<String?> getCompanyPoster() async {
    try {
      // Try local database first
      final localSettings = await _localDb.query(
        'company_settings',
        where: 'key = ?',
        whereArgs: ['company_poster'],
        limit: 1,
      );

      if (localSettings.isNotEmpty) {
        return localSettings.first['value'] as String?;
      }

      // If online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.firestore
            .collection('company_settings')
            .doc('company_poster')
            .get();

        if (doc.exists) {
          final posterUrl = doc.data()?['url'] as String?;

          // Cache locally
          if (posterUrl != null) {
            await _localDb.insert('company_settings', {
              'key': 'company_poster',
              'value': posterUrl,
              'updatedAt': DateTime.now().toIso8601String(),
            });
          }

          return posterUrl;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting company poster: $e');
      }
      return null;
    }
  }

  /// Update company poster (Super Admin only)
  Future<String> updateCompanyPoster(File imageFile) async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      // Check permissions
      if (currentUser.role != AppConstants.superAdminRole) {
        throw 'فقط المدير الأعلى يمكنه تحديث بوستر المؤسسة';
      }

      // Validate file
      if (!imageFile.existsSync()) {
        throw 'ملف الصورة غير موجود';
      }

      final fileSize = await imageFile.length();
      if (fileSize > 5 * 1024 * 1024) { // 5MB limit
        throw 'حجم الصورة كبير جداً (الحد الأقصى: 5 ميجابايت)';
      }

      String posterUrl;

      if (await _isOnline()) {
        // Upload to Firebase Storage
        posterUrl = await _uploadPosterToFirebase(imageFile);

        // Save to Firebase Firestore
        await _firebaseService.firestore
            .collection('company_settings')
            .doc('company_poster')
            .set({
          'url': posterUrl,
          'updatedAt': FieldValue.serverTimestamp(),
          'updatedBy': currentUser.id,
        });
      } else {
        // For offline mode, save locally and queue for sync
        posterUrl = 'local://${imageFile.path}';

        // Add to sync queue
        await _localDb.addToSyncQueue(
          'company_settings',
          'company_poster',
          'UPDATE',
          {
            'key': 'company_poster',
            'imageFile': imageFile.path,
            'updatedBy': currentUser.id,
          },
        );
      }

      // Update local cache
      await _localDb.delete('company_settings', 'key = ?', ['company_poster']);
      await _localDb.insert('company_settings', {
        'key': 'company_poster',
        'value': posterUrl,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('Company poster updated successfully: $posterUrl');
      }

      return posterUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating company poster: $e');
      }
      rethrow;
    }
  }

  /// Upload poster image to Firebase Storage
  Future<String> _uploadPosterToFirebase(File imageFile) async {
    try {
      final fileName = 'company_poster_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final storageRef = FirebaseStorage.instance.ref().child('company_posters/$fileName');

      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;

      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading poster to Firebase: $e');
      }
      throw 'فشل في رفع الصورة: $e';
    }
  }

  /// Search invoices in specific field
  Future<List<InvoiceModel>> searchInvoicesInSpecificField(String field, String query) async {
    try {
      List<Map<String, dynamic>> results = [];

      switch (field) {
        case 'customer_name':
          results = await _localDb.query(
            'invoices',
            where: 'customerData LIKE ?',
            whereArgs: ['%"name":"%$query%"%'],
          );
          break;
        case 'customer_id':
          results = await _localDb.query(
            'invoices',
            where: 'customerData LIKE ?',
            whereArgs: ['%"nationalId":"%$query%"%'],
          );
          break;
        case 'customer_phone':
          results = await _localDb.query(
            'invoices',
            where: 'customerData LIKE ?',
            whereArgs: ['%"phone":"%$query%"%'],
          );
          break;
        case 'invoice_number':
          results = await _localDb.query(
            'invoices',
            where: 'invoiceNumber LIKE ?',
            whereArgs: ['%$query%'],
          );
          break;
        case 'motor_fingerprint':
          // First get items with matching motor fingerprint
          final items = await _localDb.query(
            'items',
            where: 'motorFingerprintText LIKE ?',
            whereArgs: ['%$query%'],
          );

          if (items.isNotEmpty) {
            final itemIds = items.map((item) => "'${item['id']}'").join(',');
            results = await _localDb.query(
              'invoices',
              where: 'itemId IN ($itemIds)',
            );
          }
          break;
        case 'chassis_number':
          // First get items with matching chassis number
          final items = await _localDb.query(
            'items',
            where: 'chassisNumber LIKE ?',
            whereArgs: ['%$query%'],
          );

          if (items.isNotEmpty) {
            final itemIds = items.map((item) => "'${item['id']}'").join(',');
            results = await _localDb.query(
              'invoices',
              where: 'itemId IN ($itemIds)',
            );
          }
          break;
      }

      return results.map((data) => InvoiceModel.fromMap(data)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error searching invoices: $e');
      }
      rethrow;
    }
  }

  /// Get all invoices
  Future<List<InvoiceModel>> getInvoices() async {
    try {
      final results = await _localDb.query('invoices', orderBy: 'createdAt DESC');
      return results.map((data) => InvoiceModel.fromMap(data)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting invoices: $e');
      }
      rethrow;
    }
  }

  /// Create user
  Future<void> createUser(UserModel user) async {
    try {
      // Check permissions
      if (!_authService.hasPermission('user_management')) {
        throw 'ليس لديك صلاحية لإنشاء المستخدمين';
      }

      // Save to local database
      await _localDb.insert('users', user.toMap());

      // If online, save to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .doc(user.id)
            .set(user.toFirestore());

        await _localDb.markAsSynced('users', user.id);
      } else {
        // Add to sync queue for later
        await _localDb.addToSyncQueue('users', user.id, 'INSERT', user.toMap());
      }

      if (kDebugMode) {
        print('User created: ${user.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating user: $e');
      }
      rethrow;
    }
  }

  /// Update warehouse
  Future<void> updateWarehouse(String warehouseId, Map<String, dynamic> updates) async {
    try {
      updates['updatedAt'] = DateTime.now().toIso8601String();

      await _localDb.update(
        'warehouses',
        updates,
        'id = ?',
        [warehouseId],
      );

      // Sync to Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance
              .collection('warehouses')
              .doc(warehouseId)
              .update({
            ...updates,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          debugPrint('Failed to sync warehouse update to Firebase: $e');
        }
      }

      debugPrint('Warehouse updated successfully: $warehouseId');
    } catch (e) {
      throw Exception('فشل في تحديث المخزن: $e');
    }
  }



  /// Delete user
  Future<void> deleteUser(String userId) async {
    try {
      // Delete from local database
      await _localDb.deleteUser(userId);

      // Delete from Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance.collection('users').doc(userId).delete();
        } catch (e) {
          debugPrint('Failed to delete user from Firebase: $e');
        }
      }
    } catch (e) {
      throw Exception('فشل في حذف المستخدم: $e');
    }
  }

  /// Get all users with Firebase sync
  Future<List<UserModel>> getAllUsers() async {
    try {
      // First try to get from local database
      List<UserModel> users = [];

      try {
        final localUsers = await _localDb.getAllUsers();
        users = localUsers.map((userData) => UserModel.fromMap(userData)).toList();
      } catch (e) {
        debugPrint('Error getting local users: $e');
      }

      // If online, sync with Firebase
      if (await _isOnline()) {
        try {
          await _syncUsersFromFirebase();
          // Re-fetch from local after sync
          final updatedUsers = await _localDb.getAllUsers();
          users = updatedUsers.map((userData) => UserModel.fromMap(userData)).toList();
        } catch (e) {
          debugPrint('Failed to sync users from Firebase: $e');
          // Continue with local data if sync fails
        }
      }

      // If still no users, they should be created through Firebase
      if (users.isEmpty) {
        if (kDebugMode) {
          print('No users found. Please create users through Firebase Console or admin interface.');
        }
      }

      if (kDebugMode) {
        print('Loaded ${users.length} users (${users.where((u) => u.role == 'agent').length} agents)');
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all users: $e');
      }
      throw Exception('فشل في تحميل المستخدمين: $e');
    }
  }

  /// Sync users from Firebase to local database
  Future<void> _syncUsersFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore
          .collection(AppConstants.usersCollection)
          .get();

      for (final doc in snapshot.docs) {
        try {
          final user = UserModel.fromFirestore(doc);

          // Insert or update user in local database
          try {
            await _localDb.insert('users', user.toMap());
          } catch (e) {
            // If insert fails (duplicate), try update
            await _localDb.update(
              'users',
              user.toMap(),
              'id = ?',
              [user.id],
            );
          }
        } catch (e) {
          debugPrint('Error processing user ${doc.id}: $e');
          // Continue with next user
        }
      }

      if (kDebugMode) {
        print('Synced ${snapshot.docs.length} users from Firebase');
      }
    } catch (e) {
      debugPrint('Error syncing users from Firebase: $e');
      rethrow;
    }
  }



  /// Public method to sync users from Firebase
  Future<void> syncUsersFromFirebase() async {
    await _syncUsersFromFirebase();
  }

  /// Public method to sync warehouses from Firebase
  Future<void> syncWarehousesFromFirebase() async {
    await _syncWarehousesFromFirebase();
    // Note: Agent warehouses should be created manually through Firebase Console
  }



  /// Sync items from Firebase to local database
  Future<void> syncItemsFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore
          .collection(AppConstants.itemsCollection)
          .get();

      for (final doc in snapshot.docs) {
        try {
          final item = ItemModel.fromFirestore(doc);

          // Insert or update item in local database
          try {
            await _localDb.insert('items', item.toMap());
          } catch (e) {
            // If insert fails (duplicate), try update
            await _localDb.update(
              'items',
              item.toMap(),
              'id = ?',
              [item.id],
            );
          }
        } catch (e) {
          debugPrint('Error processing item ${doc.id}: $e');
          // Continue with next item
        }
      }

      if (kDebugMode) {
        print('Synced ${snapshot.docs.length} items from Firebase');
      }
    } catch (e) {
      debugPrint('Error syncing items from Firebase: $e');
      rethrow;
    }
  }

  /// Sync invoices from Firebase to local database
  Future<void> syncInvoicesFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore
          .collection(AppConstants.invoicesCollection)
          .get();

      for (final doc in snapshot.docs) {
        try {
          // Validate document data before processing
          final data = doc.data();
          if (data.isEmpty) {
            if (kDebugMode) {
              print('Skipping invoice ${doc.id}: empty data');
            }
            continue;
          }

          // Check for required fields
          if (!data.containsKey('invoiceNumber')) {
            if (kDebugMode) {
              print('Skipping invoice ${doc.id}: missing required fields');
            }
            continue;
          }

          // Handle problematic customerData and additionalData
          if (data.containsKey('customerData') && data['customerData'] is String) {
            if (kDebugMode) {
              print('Converting customerData from String to Map for invoice ${doc.id}');
            }
          }

          if (data.containsKey('additionalData') && data['additionalData'] is String) {
            if (kDebugMode) {
              print('Converting additionalData from String to Map for invoice ${doc.id}');
            }
          }

          final invoice = InvoiceModel.fromFirestore(doc);

          // Insert or update invoice in local database
          try {
            await _localDb.insert('invoices', invoice.toMap());
          } catch (e) {
            // If insert fails (duplicate), try update
            await _localDb.update(
              'invoices',
              invoice.toMap(),
              'id = ?',
              [invoice.id],
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error processing invoice ${doc.id}: $e');
            print('Document data: ${doc.data()}');
          }
          // Continue with next invoice
        }
      }

      if (kDebugMode) {
        print('Synced ${snapshot.docs.length} invoices from Firebase');
      }
    } catch (e) {
      debugPrint('Error syncing invoices from Firebase: $e');
      rethrow;
    }
  }

  /// Sync notifications from Firebase to local database
  Future<void> syncNotificationsFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore
          .collection('notifications')
          .get();

      for (final doc in snapshot.docs) {
        try {
          final notification = NotificationModel.fromFirestore(doc);

          // Insert or update notification in local database
          try {
            await _localDb.insert('notifications', notification.toMap());
          } catch (e) {
            // If insert fails (duplicate), try update
            await _localDb.update(
              'notifications',
              notification.toMap(),
              'id = ?',
              [notification.id],
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error processing notification ${doc.id}: $e');
          }
          // Continue with next notification
        }
      }

      if (kDebugMode) {
        print('Synced ${snapshot.docs.length} notifications from Firebase');
      }
    } catch (e) {
      debugPrint('Error syncing notifications from Firebase: $e');
      rethrow;
    }
  }

  /// Create warehouse
  Future<String> createWarehouse(WarehouseModel warehouse) async {
    try {
      final warehouseId = AppUtils.generateId();
      final warehouseWithId = warehouse.copyWith(id: warehouseId);

      // Create in local database
      await _localDb.createWarehouse(warehouseWithId.toMap());

      // Create in Firebase if online
      if (await _isOnline()) {
        try {
          await FirebaseFirestore.instance.collection('warehouses').doc(warehouseId).set({
            ...warehouseWithId.toMap(),
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        } catch (e) {
          debugPrint('Failed to sync warehouse to Firebase: $e');
        }
      }

      return warehouseId;
    } catch (e) {
      throw Exception('فشل في إنشاء المخزن: $e');
    }
  }

  /// Update user
  Future<void> updateUser(UserModel user) async {
    try {
      if (kDebugMode) {
        print('🔄 DataService.updateUser called for: ${user.fullName}');
        print('   User ID: ${user.id}');
        print('   Profit Share: ${(user.profitSharePercentage * 100).toStringAsFixed(1)}%');
        print('   User data: ${user.toMap()}');
      }

      // Update in local database
      await _localDb.updateUser(user.id, user.toMap());

      if (kDebugMode) {
        print('✅ Local database updated successfully');
      }

      // Update in Firebase if online
      if (await _isOnline()) {
        try {
          final firebaseData = {
            ...user.toFirestore(), // Use toFirestore() instead of toMap()
            'updatedAt': FieldValue.serverTimestamp(),
          };

          if (kDebugMode) {
            print('🔄 Updating Firebase with data: $firebaseData');
          }

          await FirebaseFirestore.instance.collection('users').doc(user.id).update(firebaseData);

          if (kDebugMode) {
            print('✅ Firebase updated successfully');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to sync user update to Firebase: $e');
          }
          debugPrint('Failed to sync user update to Firebase: $e');
        }
      } else {
        if (kDebugMode) {
          print('📴 Offline - Firebase update skipped');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in updateUser: $e');
      }
      throw Exception('فشل في تحديث المستخدم: $e');
    }
  }

  /// Transfer items between warehouses (legacy method for quantity-based transfers)
  Future<void> transferItemsBetweenWarehouses({
    required String sourceWarehouseId,
    required String targetWarehouseId,
    required String itemId,
    required int quantity,
    required String createdBy,
    String? notes,
  }) async {
    try {
      // Validate source warehouse has enough stock
      final sourceStock = await _getWarehouseItemStock(sourceWarehouseId, itemId);
      if (sourceStock < quantity) {
        throw Exception('المخزن المصدر لا يحتوي على كمية كافية');
      }

      // Get item and warehouse details
      final item = await getItemById(itemId);
      final sourceWarehouse = await _getWarehouseById(sourceWarehouseId);
      final targetWarehouse = await _getWarehouseById(targetWarehouseId);

      if (item == null || sourceWarehouse == null || targetWarehouse == null) {
        throw Exception('بيانات الصنف أو المخزن غير صحيحة');
      }

      // Create inventory movement record
      final movement = InventoryMovementModel(
        id: '${DateTime.now().millisecondsSinceEpoch}_transfer',
        itemId: itemId,
        itemName: item.displayName,
        brand: item.brand,
        model: item.model,
        movementType: 'transfer',
        quantity: quantity,
        sourceWarehouseId: sourceWarehouseId,
        targetWarehouseId: targetWarehouseId,
        sourceWarehouseName: sourceWarehouse.name,
        targetWarehouseName: targetWarehouse.name,
        reason: 'transfer',
        timestamp: DateTime.now(),
        createdBy: createdBy,
        notes: notes,
      );

      await recordInventoryMovement(movement);

      if (kDebugMode) {
        print('Items transferred: ${item.displayName} x$quantity from ${sourceWarehouse.name} to ${targetWarehouse.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error transferring items: $e');
      }
      rethrow;
    }
  }

  /// Get warehouse by ID
  Future<WarehouseModel?> _getWarehouseById(String warehouseId) async {
    try {
      // Validate warehouseId
      if (warehouseId.isEmpty) {
        if (kDebugMode) {
          print('Error getting warehouse by ID: warehouseId is empty');
        }
        return null;
      }

      final warehouses = await _localDb.query(
        'warehouses',
        where: 'id = ?',
        whereArgs: [warehouseId],
      );

      if (warehouses.isNotEmpty) {
        return WarehouseModel.fromMap(warehouses.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse by ID: $e');
      }
      return null;
    }
  }

  /// Get all warehouses
  Future<List<WarehouseModel>> getAllWarehouses() async {
    try {
      final warehouses = await _localDb.query('warehouses');
      return warehouses.map((map) => WarehouseModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all warehouses: $e');
      }
      return [];
    }
  }

  /// Get items in a specific warehouse
  Future<List<ItemModel>> getWarehouseItems(String warehouseId) async {
    try {
      final items = await _localDb.query(
        'items',
        where: 'currentWarehouseId = ?',
        whereArgs: [warehouseId],
      );
      return items.map((map) => ItemModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse items: $e');
      }
      return [];
    }
  }





  // Search customer invoices with multiple criteria
  Future<List<InvoiceModel>> searchCustomerInvoices({
    required String query,
    required String searchType,
  }) async {
    try {
      List<InvoiceModel> results = [];

      if (searchType == 'all') {
        // Search in all fields
        results = await _searchInAllFields(query);
      } else {
        // Search in specific field
        results = await _searchInSpecificField(query, searchType);
      }

      // Sort by creation date (newest first)
      results.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (kDebugMode) {
        print('Found ${results.length} invoices for customer search: $query');
      }

      return results;
    } catch (e) {
      if (kDebugMode) {
        print('Error searching customer invoices: $e');
      }
      rethrow;
    }
  }

  // Get customer transaction history (all invoices and related data)
  Future<Map<String, dynamic>> getCustomerTransactionHistory({
    required String query,
    required String searchType,
  }) async {
    try {
      // Get customer invoices
      final invoices = await searchCustomerInvoices(
        query: query,
        searchType: searchType,
      );

      // Calculate totals
      double totalPurchases = 0;
      int totalTransactions = invoices.length;
      DateTime? firstPurchase;
      DateTime? lastPurchase;

      for (final invoice in invoices) {
        totalPurchases += invoice.sellingPrice;

        if (firstPurchase == null || invoice.createdAt.isBefore(firstPurchase)) {
          firstPurchase = invoice.createdAt;
        }

        if (lastPurchase == null || invoice.createdAt.isAfter(lastPurchase)) {
          lastPurchase = invoice.createdAt;
        }
      }

      // Get document tracking for invoices
      List<DocumentTrackingModel> documentTracking = [];
      for (final invoice in invoices) {
        final tracking = await getDocumentTrackingByInvoiceId(invoice.id);
        if (tracking != null) {
          documentTracking.add(tracking);
        }
      }

      return {
        'invoices': invoices,
        'documentTracking': documentTracking,
        'summary': {
          'totalPurchases': totalPurchases,
          'totalTransactions': totalTransactions,
          'firstPurchase': firstPurchase,
          'lastPurchase': lastPurchase,
          'averagePurchase': totalTransactions > 0 ? totalPurchases / totalTransactions : 0,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting customer transaction history: $e');
      }
      rethrow;
    }
  }

  Future<List<InvoiceModel>> _searchInAllFields(String query) async {
    final Set<String> invoiceIds = <String>{};
    final List<InvoiceModel> results = [];

    // Search by invoice number
    final invoiceNumberResults = await _searchInSpecificField(query, 'invoice_number');
    for (final invoice in invoiceNumberResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    // Search by customer name
    final customerNameResults = await _searchInSpecificField(query, 'customer_name');
    for (final invoice in customerNameResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    // Search by national ID
    final nationalIdResults = await _searchInSpecificField(query, 'national_id');
    for (final invoice in nationalIdResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    // Search by motor fingerprint
    final motorResults = await _searchInSpecificField(query, 'motor_fingerprint');
    for (final invoice in motorResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    // Search by chassis number
    final chassisResults = await _searchInSpecificField(query, 'chassis_number');
    for (final invoice in chassisResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    // Search by phone
    final phoneResults = await _searchInSpecificField(query, 'phone');
    for (final invoice in phoneResults) {
      if (!invoiceIds.contains(invoice.id)) {
        invoiceIds.add(invoice.id);
        results.add(invoice);
      }
    }

    return results;
  }

  Future<List<InvoiceModel>> _searchInSpecificField(String query, String searchType) async {
    try {
      String whereClause = '';
      List<dynamic> whereArgs = [];

      switch (searchType) {
        case 'invoice_number':
          whereClause = 'invoiceNumber LIKE ?';
          whereArgs = ['%$query%'];
          break;
        case 'customer_name':
          whereClause = 'customerData LIKE ?';
          whereArgs = ['%"fullName":"$query"%'];
          break;
        case 'national_id':
          whereClause = 'customerData LIKE ?';
          whereArgs = ['%"nationalId":"$query"%'];
          break;
        case 'phone':
          whereClause = 'customerData LIKE ?';
          whereArgs = ['%"phone":"$query"%'];
          break;
        case 'motor_fingerprint':
          // First get items with matching motor fingerprint
          final items = await _localDb.query(
            'items',
            where: 'motorFingerprintText LIKE ?',
            whereArgs: ['%$query%'],
          );

          if (items.isEmpty) return [];

          final itemIds = items.map((item) => "'${item['id']}'").join(',');
          whereClause = 'itemId IN ($itemIds)';
          break;
        case 'chassis_number':
          // First get items with matching chassis number
          final items = await _localDb.query(
            'items',
            where: 'chassisNumber LIKE ?',
            whereArgs: ['%$query%'],
          );

          if (items.isEmpty) return [];

          final itemIds = items.map((item) => "'${item['id']}'").join(',');
          whereClause = 'itemId IN ($itemIds)';
          break;
        default:
          return [];
      }

      // Search in local database
      final localResults = await _localDb.query(
        'invoices',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'createdAt DESC',
      );

      List<InvoiceModel> invoices = localResults.map((data) => InvoiceModel.fromMap(data)).toList();

      // If online, also search in Firebase
      if (await _isOnline()) {
        try {
          Query<Map<String, dynamic>> firestoreQuery = _firebaseService.firestore
              .collection(AppConstants.invoicesCollection);

          // Note: Firestore has limited query capabilities
          // For complex searches, we might need to fetch all and filter locally
          if (searchType == 'invoice_number') {
            firestoreQuery = firestoreQuery
                .where('invoiceNumber', isGreaterThanOrEqualTo: query)
                .where('invoiceNumber', isLessThan: '$query\uf8ff');
          }

          final snapshot = await firestoreQuery.get();
          final firebaseInvoices = <InvoiceModel>[];

          for (final doc in snapshot.docs) {
            try {
              final data = doc.data();
              if (data.isNotEmpty && data.containsKey('id')) {
                final invoice = InvoiceModel.fromFirestore(doc);
                firebaseInvoices.add(invoice);
              }
            } catch (e) {
              if (kDebugMode) {
                print('Error processing Firebase invoice ${doc.id}: $e');
              }
              // Skip this document and continue
            }
          }

          // Merge results and remove duplicates
          final allInvoices = <String, InvoiceModel>{};
          for (final invoice in invoices) {
            allInvoices[invoice.id] = invoice;
          }
          for (final invoice in firebaseInvoices) {
            allInvoices[invoice.id] = invoice;
          }

          invoices = allInvoices.values.toList();
        } catch (e) {
          if (kDebugMode) {
            print('Error searching in Firebase: $e');
          }
          // Continue with local results only
        }
      }

      return invoices;
    } catch (e) {
      if (kDebugMode) {
        print('Error searching in specific field: $e');
      }
      rethrow;
    }
  }

  // Get document tracking by invoice ID
  Future<DocumentTrackingModel?> getDocumentTrackingByInvoiceId(String invoiceId) async {
    try {
      // Search in local database first
      final localResults = await _localDb.query(
        'document_tracking',
        where: 'invoiceId = ?',
        whereArgs: [invoiceId],
        limit: 1,
      );

      if (localResults.isNotEmpty) {
        return DocumentTrackingModel.fromMap(localResults.first);
      }

      // If not found locally and online, search in Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.firestore
            .collection(AppConstants.documentTrackingCollection)
            .where('invoiceId', isEqualTo: invoiceId)
            .limit(1)
            .get();

        if (snapshot.docs.isNotEmpty) {
          return DocumentTrackingModel.fromFirestore(snapshot.docs.first);
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting document tracking by invoice ID: $e');
      }
      return null;
    }
  }

  /// Sync invoices from Firebase
  Future<void> _syncInvoicesFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore.collection(AppConstants.invoicesCollection).get();
      final invoices = snapshot.docs.map((doc) => InvoiceModel.fromFirestore(doc)).toList();

      await _updateLocalFromFirebase('invoices', invoices.map((i) => i.toMap()).toList());

      if (kDebugMode) {
        print('✅ Synced ${invoices.length} invoices from Firebase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing invoices: $e');
      }
    }
  }

  /// Sync agent accounts from Firebase
  Future<void> _syncAgentAccountsFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore.collection(AppConstants.agentAccountsCollection).get();
      final accounts = snapshot.docs.map((doc) => AgentAccountModel.fromFirestore(doc)).toList();

      await _updateLocalFromFirebase('agent_accounts', accounts.map((a) => a.toMap()).toList());

      if (kDebugMode) {
        print('✅ Synced ${accounts.length} agent accounts from Firebase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing agent accounts: $e');
      }
    }
  }

  /// Sync payments from Firebase
  Future<void> _syncPaymentsFromFirebase() async {
    try {
      final snapshot = await _firebaseService.firestore.collection(AppConstants.paymentsCollection).get();
      final payments = snapshot.docs.map((doc) => PaymentModel.fromFirestore(doc)).toList();

      await _updateLocalFromFirebase('agent_payments', payments.map((p) => p.toMap()).toList());

      if (kDebugMode) {
        print('✅ Synced ${payments.length} payments from Firebase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing payments: $e');
      }
    }
  }

  /// Clear local-only tables that don't exist in Firebase
  Future<void> _clearLocalOnlyTables() async {
    try {
      if (kDebugMode) {
        print('🧹 Clearing local-only tables...');
      }

      // Tables that exist only locally and should be cleared
      final localOnlyTables = [
        'reports',
        'inventory_movements',
        'warehouse_items',
        'transfers',
        'agent_transfer_invoices',
        'sync_queue',
        'settings'
      ];

      for (final tableName in localOnlyTables) {
        try {
          await _localDb.delete(tableName, '1=1', []);
          if (kDebugMode) {
            print('✅ Cleared table: $tableName');
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Could not clear table $tableName: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Local-only tables cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing local-only tables: $e');
      }
    }
  }

  /// Complete database cleanup - clear all local data and sync from Firebase
  Future<void> completeCleanupAndSync() async {
    if (!await _isOnline()) {
      throw 'لا يوجد اتصال بالإنترنت. يجب الاتصال بالإنترنت لتنظيف البيانات.';
    }

    try {
      if (kDebugMode) {
        print('🧹 Starting complete database cleanup...');
      }

      // Clear ALL local data
      await _localDb.clearAllData();

      // Force recreate notifications table to fix schema issues
      await _localDb.forceRecreateNotificationsTable();

      if (kDebugMode) {
        print('✅ All local data cleared');
      }

      // Force sync everything from Firebase
      await forceSyncFromFirebase();

      if (kDebugMode) {
        print('✅ Complete cleanup and sync completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during complete cleanup: $e');
      }
      rethrow;
    }
  }

  /// Fix notifications table schema issue
  Future<void> fixNotificationsTable() async {
    try {
      if (kDebugMode) {
        print('🔧 Fixing notifications table schema...');
      }

      await _localDb.forceRecreateNotificationsTable();

      if (kDebugMode) {
        print('✅ Notifications table fixed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fixing notifications table: $e');
      }
    }
  }

  /// Update existing items with old status to new Arabic status
  Future<void> updateItemStatusesToArabic() async {
    try {
      final db = await _localDb.database;

      // Update all items with old English status to new Arabic status
      final availableCount = await db.update(
        'items',
        {'status': 'متاح'},
        where: 'status = ?',
        whereArgs: ['available'],
      );

      final soldCount = await db.update(
        'items',
        {'status': 'مباع'},
        where: 'status = ?',
        whereArgs: ['sold'],
      );

      final transferredCount = await db.update(
        'items',
        {'status': 'محول'},
        where: 'status = ?',
        whereArgs: ['transferred'],
      );

      final returnedCount = await db.update(
        'items',
        {'status': 'مرتجع'},
        where: 'status = ?',
        whereArgs: ['returned'],
      );

      if (kDebugMode) {
        print('✅ Updated item statuses to Arabic:');
        print('   Available: $availableCount items');
        print('   Sold: $soldCount items');
        print('   Transferred: $transferredCount items');
        print('   Returned: $returnedCount items');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating item statuses: $e');
      }
    }
  }

  /// Get inventory movements with optional filtering
  Future<List<InventoryMovementModel>> getInventoryMovements({
    String? movementType,
    String? warehouseId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final db = await _localDb.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (movementType != null) {
        whereClause += ' AND movementType = ?';
        whereArgs.add(movementType);
      }

      if (warehouseId != null) {
        whereClause += ' AND (sourceWarehouseId = ? OR targetWarehouseId = ?)';
        whereArgs.addAll([warehouseId, warehouseId]);
      }

      if (startDate != null) {
        whereClause += ' AND timestamp >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        whereClause += ' AND timestamp <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'inventory_movements',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'timestamp DESC',
      );

      if (kDebugMode) {
        print('🔍 getInventoryMovements query results:');
        print('   - Where clause: $whereClause');
        print('   - Where args: $whereArgs');
        print('   - Found ${maps.length} records');
        for (final map in maps) {
          print('   - ${map['movementType']}: ${map['itemName']} (${map['timestamp']})');
        }
      }

      return maps.map((map) => InventoryMovementModel.fromMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting inventory movements: $e');
      }
      return [];
    }
  }


}
