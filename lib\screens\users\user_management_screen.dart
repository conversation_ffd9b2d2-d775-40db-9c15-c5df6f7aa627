import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../services/data_service.dart';
import '../../providers/auth_provider.dart';
import '../../models/user_model.dart';
import 'add_edit_user_screen.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final DataService _dataService = DataService.instance;
  List<UserModel> _users = [];
  bool _isLoading = false;
  String _selectedRole = 'all';

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final users = await _dataService.getUsers(
        role: _selectedRole == 'all' ? null : _selectedRole,
        isActive: true,
      );
      setState(() {
        _users = users;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المستخدمين: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    if (!authProvider.canManageUsers) {
      return Scaffold(
        appBar: AppBar(title: const Text('إدارة المستخدمين')),
        body: const Center(
          child: Text(
            'ليس لديك صلاحية لإدارة المستخدمين',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المستخدمين'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddUserDialog(),
            tooltip: 'إضافة مستخدم جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter by role
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Text(
                  'تصفية حسب الدور:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: DropdownButton<String>(
                    value: _selectedRole,
                    isExpanded: true,
                    onChanged: (value) {
                      setState(() {
                        _selectedRole = value!;
                      });
                      _loadUsers();
                    },
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع الأدوار')),
                      DropdownMenuItem(value: 'super_admin', child: Text('مدير أعلى')),
                      DropdownMenuItem(value: 'admin', child: Text('مدير')),
                      DropdownMenuItem(value: 'agent', child: Text('وكيل')),
                      DropdownMenuItem(value: 'showroom', child: Text('معرض')),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Users list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _users.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد مستخدمين',
                          style: TextStyle(fontSize: 18),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        itemCount: _users.length,
                        itemBuilder: (context, index) {
                          final user = _users[index];
                          return _buildUserCard(user);
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Icon(
            Icons.person,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        title: Text(
          user.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم المستخدم: ${user.username}'),
            Text('البريد الإلكتروني: ${user.email}'),
            Text('الدور: ${_getRoleDisplayName(user.role)}'),
            Text('الهاتف: ${user.phone}'),
            // Show profit share for agents
            if (user.role == 'agent')
              Text(
                'نسبة الربح: ${(user.profitSharePercentage * 100).toStringAsFixed(0)}%',
                style: const TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (action) => _handleUserAction(action, user),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            // Show profit share option only for agents
            if (user.role == 'agent')
              const PopupMenuItem(
                value: 'profit_share',
                child: Row(
                  children: [
                    Icon(Icons.percent, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تعديل نسبة الربح'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'deactivate',
              child: Row(
                children: [
                  Icon(Icons.block, color: Colors.red),
                  SizedBox(width: 8),
                  Text('إلغاء تفعيل'),
                ],
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'super_admin':
        return 'مدير أعلى';
      case 'admin':
        return 'مدير';
      case 'agent':
        return 'وكيل';
      case 'showroom':
        return 'معرض';
      default:
        return role;
    }
  }

  void _handleUserAction(String action, UserModel user) {
    switch (action) {
      case 'edit':
        _showEditUserDialog(user);
        break;
      case 'profit_share':
        _showEditProfitShareDialog(user);
        break;
      case 'deactivate':
        _showDeactivateUserDialog(user);
        break;
    }
  }

  void _showAddUserDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditUserScreen(),
      ),
    ).then((_) => _loadUsers());
  }

  void _showEditUserDialog(UserModel user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditUserScreen(user: user),
      ),
    ).then((_) => _loadUsers());
  }

  void _showEditProfitShareDialog(UserModel user) {
    final TextEditingController profitController = TextEditingController(
      text: (user.profitSharePercentage * 100).toStringAsFixed(0),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل نسبة ربح الوكيل: ${user.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'النسبة الحالية: ${(user.profitSharePercentage * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: profitController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'نسبة ربح الوكيل (%)',
                hintText: 'أدخل النسبة من 0 إلى 100',
                border: OutlineInputBorder(),
                suffixText: '%',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'ملاحظة: النسبة المتبقية ستكون للمؤسسة',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final percentageText = profitController.text.trim();
              if (percentageText.isEmpty) {
                AppUtils.showSnackBar(context, 'يرجى إدخال النسبة', isError: true);
                return;
              }

              final percentage = double.tryParse(percentageText);
              if (percentage == null || percentage < 0 || percentage > 100) {
                AppUtils.showSnackBar(context, 'يرجى إدخال نسبة صحيحة من 0 إلى 100', isError: true);
                return;
              }

              try {
                final updatedUser = user.copyWith(
                  profitSharePercentage: percentage / 100, // تحويل إلى عشري
                  updatedAt: DateTime.now(),
                );

                await _dataService.updateUser(updatedUser);

                if (mounted) {
                  Navigator.pop(context);
                  AppUtils.showSnackBar(context, 'تم تحديث نسبة الربح بنجاح');

                  // إعادة تحميل البيانات
                  _loadUsers();
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showSnackBar(context, 'خطأ في تحديث نسبة الربح: $e', isError: true);
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showDeactivateUserDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء تفعيل المستخدم'),
        content: Text('هل أنت متأكد من إلغاء تفعيل ${user.fullName}؟\n\nهذه الميزة قيد التطوير'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              AppUtils.showSnackBar(context, 'تم إلغاء تفعيل المستخدم (تجريبي)');
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}
