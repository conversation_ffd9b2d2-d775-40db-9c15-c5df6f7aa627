import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';


class LocalDatabaseService {
  static LocalDatabaseService? _instance;
  static LocalDatabaseService get instance => _instance ??= LocalDatabaseService._();
  
  LocalDatabaseService._();

  Database? _database;

  Future<void> initialize() async {
    try {
      _database = await _initDatabase();
      if (kDebugMode) {
        print('LocalDatabaseService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing LocalDatabaseService: $e');
      }
      rethrow;
    }
  }

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    await _checkAndFixDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      String path = join(await getDatabasesPath(), 'el_farhan_app.db');

      if (kDebugMode) {
        print('🗄️ Initializing database at: $path');
        print('🗄️ Target version: 8');
      }

      return await openDatabase(
        path,
        version: 9, // رفع الإصدار لإضافة نسبة الربح
        onCreate: _createTables,
        onUpgrade: _upgradeDatabase,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing database: $e');
      }
      rethrow;
    }
  }

  Future<void> _createTables(Database db, int version) async {
    try {
      // Users table
      await db.execute('''
        CREATE TABLE users (
          id TEXT PRIMARY KEY,
          username TEXT NOT NULL,
          email TEXT NOT NULL,
          fullName TEXT NOT NULL,
          phone TEXT NOT NULL,
          role TEXT NOT NULL,
          warehouseId TEXT,
          passwordHash TEXT,
          isActive INTEGER NOT NULL DEFAULT 1,
          profitSharePercentage REAL DEFAULT 0.5,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Warehouses table
      await db.execute('''
        CREATE TABLE warehouses (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          ownerId TEXT,
          address TEXT NOT NULL,
          phone TEXT,
          email TEXT,
          isActive INTEGER NOT NULL DEFAULT 1,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Items table
      await db.execute('''
        CREATE TABLE items (
          id TEXT PRIMARY KEY,
          type TEXT NOT NULL,
          model TEXT NOT NULL,
          color TEXT NOT NULL,
          brand TEXT NOT NULL,
          countryOfOrigin TEXT NOT NULL,
          yearOfManufacture INTEGER NOT NULL,
          purchasePrice REAL NOT NULL,
          suggestedSellingPrice REAL NOT NULL,
          motorFingerprintImageUrl TEXT NOT NULL,
          motorFingerprintText TEXT NOT NULL,
          chassisImageUrl TEXT NOT NULL DEFAULT '',
          chassisNumber TEXT NOT NULL DEFAULT '',
          currentWarehouseId TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'available',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Invoices table
      await db.execute('''
        CREATE TABLE invoices (
          id TEXT PRIMARY KEY,
          invoiceNumber TEXT NOT NULL,
          type TEXT NOT NULL,
          customerId TEXT,
          agentId TEXT,
          warehouseId TEXT NOT NULL,
          itemId TEXT NOT NULL,
          itemCost REAL NOT NULL,
          sellingPrice REAL NOT NULL,
          profitAmount REAL NOT NULL,
          companyProfitShare REAL NOT NULL,
          agentProfitShare REAL NOT NULL DEFAULT 0,
          status TEXT NOT NULL DEFAULT 'pending',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          customerData TEXT,
          customerIdImages TEXT,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Payments table
      await db.execute('''
        CREATE TABLE payments (
          id TEXT PRIMARY KEY,
          receiptNumber TEXT NOT NULL,
          agentId TEXT NOT NULL,
          amount REAL NOT NULL,
          paymentMethod TEXT NOT NULL,
          paymentDate TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          notes TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          confirmedBy TEXT,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Document tracking table
      await db.execute('''
        CREATE TABLE document_tracking (
          id TEXT PRIMARY KEY,
          itemId TEXT NOT NULL,
          invoiceId TEXT NOT NULL,
          currentStatus TEXT NOT NULL,
          statusHistory TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          compositeImagePath TEXT,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Sync queue table for offline operations
      await db.execute('''
        CREATE TABLE sync_queue (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tableName TEXT NOT NULL,
          recordId TEXT NOT NULL,
          operation TEXT NOT NULL,
          data TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          retryCount INTEGER NOT NULL DEFAULT 0
        )
      ''');



      // Settings table for app configuration
      await db.execute('''
        CREATE TABLE IF NOT EXISTS settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL
        )
      ''');

      // Agent accounts table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS agent_accounts (
          id TEXT PRIMARY KEY,
          agentId TEXT NOT NULL,
          agentName TEXT NOT NULL,
          agentPhone TEXT,
          totalDebt REAL NOT NULL DEFAULT 0,
          totalPaid REAL NOT NULL DEFAULT 0,
          currentBalance REAL NOT NULL DEFAULT 0,
          transactions TEXT NOT NULL DEFAULT '[]',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT,
          additionalData TEXT,
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Agent transfer invoices table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS agent_transfer_invoices (
          id TEXT PRIMARY KEY,
          invoiceNumber TEXT NOT NULL,
          agentId TEXT NOT NULL,
          agentName TEXT NOT NULL,
          items TEXT NOT NULL,
          totalAgentPrice REAL NOT NULL,
          totalSellingPrice REAL NOT NULL,
          totalProfit REAL NOT NULL,
          createdBy TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Inventory movements table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS inventory_movements (
          id TEXT PRIMARY KEY,
          itemId TEXT NOT NULL,
          itemName TEXT NOT NULL,
          sourceWarehouseId TEXT NOT NULL,
          targetWarehouseId TEXT,
          quantity INTEGER NOT NULL,
          movementType TEXT NOT NULL,
          reason TEXT,
          createdBy TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Warehouse items table (for stock tracking)
      await db.execute('''
        CREATE TABLE IF NOT EXISTS warehouse_items (
          id TEXT PRIMARY KEY,
          warehouseId TEXT NOT NULL,
          itemId TEXT NOT NULL,
          quantity INTEGER NOT NULL DEFAULT 0,
          lastUpdated TEXT NOT NULL,
          syncStatus INTEGER DEFAULT 0,
          UNIQUE(warehouseId, itemId)
        )
      ''');

      // Transfers table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS transfers (
          id TEXT PRIMARY KEY,
          sourceWarehouseId TEXT NOT NULL,
          targetWarehouseId TEXT NOT NULL,
          itemId TEXT NOT NULL,
          quantity INTEGER NOT NULL DEFAULT 1,
          createdBy TEXT NOT NULL,
          notes TEXT,
          createdAt TEXT NOT NULL,
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Notifications table (unified version)
      await db.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
          id TEXT PRIMARY KEY,
          userId TEXT,
          targetUserId TEXT,
          targetRole TEXT,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL DEFAULT 'general',
          relatedId TEXT,
          data TEXT,
          isRead INTEGER NOT NULL DEFAULT 0,
          readAt TEXT,
          createdAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Agent payments table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS agent_payments (
          id TEXT PRIMARY KEY,
          receiptNumber TEXT,
          agentId TEXT NOT NULL,
          amount REAL NOT NULL,
          paymentMethod TEXT DEFAULT 'cash',
          paymentDate TEXT,
          notes TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT,
          createdBy TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'confirmed',
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Reports table
      await db.execute('''
        CREATE TABLE IF NOT EXISTS reports (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          type TEXT NOT NULL,
          filePath TEXT NOT NULL,
          metadata TEXT,
          createdAt TEXT NOT NULL,
          syncStatus INTEGER DEFAULT 0
        )
      ''');

      // Create indexes for better performance
      await db.execute('CREATE INDEX idx_users_role ON users(role)');
      await db.execute('CREATE INDEX idx_items_warehouse ON items(currentWarehouseId)');
      await db.execute('CREATE INDEX idx_items_status ON items(status)');
      await db.execute('CREATE INDEX idx_invoices_type ON invoices(type)');
      await db.execute('CREATE INDEX idx_invoices_agent ON invoices(agentId)');
      await db.execute('CREATE INDEX idx_payments_agent ON payments(agentId)');
      await db.execute('CREATE INDEX idx_sync_queue_table ON sync_queue(tableName)');

      if (kDebugMode) {
        print('Database tables created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error creating tables: $e');
      }
      rethrow;
    }
  }

  /// Check and fix missing tables or columns
  Future<void> _checkAndFixDatabase() async {
    if (_database == null) return;

    try {
      // Ensure agent_payments table exists and has all required columns
      await _migrateAgentPaymentsTable();

      // Fix notifications table if it doesn't have userId column
      await _fixNotificationsTable();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking and fixing database: $e');
      }
    }
  }

  /// Migrate agent_payments table to ensure all required columns exist
  Future<void> _migrateAgentPaymentsTable() async {
    try {
      // Check if agent_payments table exists
      final tables = await _database!.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='agent_payments'"
      );

      if (tables.isEmpty) {
        if (kDebugMode) {
          print('Creating missing agent_payments table');
        }
        await _database!.execute('''
          CREATE TABLE IF NOT EXISTS agent_payments (
            id TEXT PRIMARY KEY,
            receiptNumber TEXT,
            agentId TEXT NOT NULL,
            amount REAL NOT NULL,
            paymentMethod TEXT DEFAULT 'cash',
            paymentDate TEXT,
            notes TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT,
            createdBy TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'confirmed',
            syncStatus INTEGER DEFAULT 0
          )
        ''');
      } else {
        // Table exists, check for missing columns and add them
        final columns = await _database!.rawQuery('PRAGMA table_info(agent_payments)');
        final columnNames = columns.map((col) => col['name'] as String).toSet();

        if (!columnNames.contains('receiptNumber')) {
          await _database!.execute('ALTER TABLE agent_payments ADD COLUMN receiptNumber TEXT');
          if (kDebugMode) print('Added receiptNumber column to agent_payments');
        }

        if (!columnNames.contains('paymentMethod')) {
          await _database!.execute('ALTER TABLE agent_payments ADD COLUMN paymentMethod TEXT DEFAULT "cash"');
          if (kDebugMode) print('Added paymentMethod column to agent_payments');
        }

        if (!columnNames.contains('paymentDate')) {
          await _database!.execute('ALTER TABLE agent_payments ADD COLUMN paymentDate TEXT');
          if (kDebugMode) print('Added paymentDate column to agent_payments');
        }

        if (!columnNames.contains('updatedAt')) {
          await _database!.execute('ALTER TABLE agent_payments ADD COLUMN updatedAt TEXT');
          if (kDebugMode) print('Added updatedAt column to agent_payments');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error migrating agent_payments table: $e');
      }
    }
  }

  /// Fix notifications table to include userId column
  Future<void> _fixNotificationsTable() async {
    try {
      // Check if notifications table exists and has correct schema
      final result = await _database!.rawQuery(
        "PRAGMA table_info(notifications)"
      );

      bool hasUserId = false;
      for (final column in result) {
        if (column['name'] == 'userId') {
          hasUserId = true;
          break;
        }
      }

      if (!hasUserId || result.isEmpty) {
        if (kDebugMode) {
          print('🔧 Recreating notifications table with correct schema');
        }

        // Drop existing table
        await _database!.execute('DROP TABLE IF EXISTS notifications');

        // Create new table with correct schema
        await _database!.execute('''
          CREATE TABLE notifications (
            id TEXT PRIMARY KEY,
            userId TEXT,
            targetUserId TEXT,
            targetRole TEXT,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            type TEXT NOT NULL DEFAULT 'general',
            relatedId TEXT,
            data TEXT,
            isRead INTEGER NOT NULL DEFAULT 0,
            readAt TEXT,
            createdAt TEXT NOT NULL,
            createdBy TEXT NOT NULL,
            syncStatus INTEGER NOT NULL DEFAULT 0
          )
        ''');

        if (kDebugMode) {
          print('✅ Notifications table recreated successfully');
        }
      } else {
        if (kDebugMode) {
          print('✅ Notifications table schema is correct');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error checking/fixing notifications table: $e');
      }
    }
  }

  /// Force recreate notifications table (for debugging)
  Future<void> forceRecreateNotificationsTable() async {
    try {
      if (kDebugMode) {
        print('🔧 Force recreating notifications table...');
      }

      // Drop existing table
      await _database!.execute('DROP TABLE IF EXISTS notifications');

      // Create new table with correct schema
      await _database!.execute('''
        CREATE TABLE notifications (
          id TEXT PRIMARY KEY,
          userId TEXT,
          targetUserId TEXT,
          targetRole TEXT,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL DEFAULT 'general',
          relatedId TEXT,
          data TEXT,
          isRead INTEGER NOT NULL DEFAULT 0,
          readAt TEXT,
          createdAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      if (kDebugMode) {
        print('✅ Notifications table force recreated successfully');
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error force recreating notifications table: $e');
      }
    }
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (kDebugMode) {
      print('🔧 Upgrading database from version $oldVersion to $newVersion');
    }

    // Add targetUserId column to notifications table if missing
    try {
      await db.execute('ALTER TABLE notifications ADD COLUMN targetUserId TEXT');
      debugPrint('Added targetUserId column to notifications table');
    } catch (e) {
      debugPrint('Column targetUserId might already exist: $e');
    }

    // Add profitSharePercentage column to users table (version 9)
    if (oldVersion < 9) {
      try {
        await db.execute('ALTER TABLE users ADD COLUMN profitSharePercentage REAL DEFAULT 0.5');
        debugPrint('Added profitSharePercentage column to users table');
      } catch (e) {
        debugPrint('Error adding profitSharePercentage column or it might already exist: $e');
      }
    }

    // Add chassis fields to items table (version 4)
    if (oldVersion < 4) {
      try {
        await db.execute('ALTER TABLE items ADD COLUMN chassisImageUrl TEXT NOT NULL DEFAULT ""');
        await db.execute('ALTER TABLE items ADD COLUMN chassisNumber TEXT NOT NULL DEFAULT ""');
        debugPrint('Added chassis fields to items table');
      } catch (e) {
        debugPrint('Error adding chassis fields or they might already exist: $e');
        // For now, recreate all tables to ensure they have the correct structure
        await _recreateAllTables(db);
      }
    }

    // Add missing columns to agent_accounts table (version 5)
    if (oldVersion < 5) {
      try {
        await db.execute('ALTER TABLE agent_accounts ADD COLUMN agentPhone TEXT');
        await db.execute('ALTER TABLE agent_accounts ADD COLUMN createdBy TEXT');
        await db.execute('ALTER TABLE agent_accounts ADD COLUMN additionalData TEXT');
        debugPrint('Added missing columns to agent_accounts table');
      } catch (e) {
        debugPrint('Error adding columns to agent_accounts or they might already exist: $e');
        // Recreate agent_accounts table if needed
        try {
          await db.execute('DROP TABLE IF EXISTS agent_accounts');
          await db.execute('''
            CREATE TABLE agent_accounts (
              id TEXT PRIMARY KEY,
              agentId TEXT NOT NULL,
              agentName TEXT NOT NULL,
              agentPhone TEXT,
              totalDebt REAL NOT NULL DEFAULT 0,
              totalPaid REAL NOT NULL DEFAULT 0,
              currentBalance REAL NOT NULL DEFAULT 0,
              transactions TEXT NOT NULL DEFAULT '[]',
              createdAt TEXT NOT NULL,
              updatedAt TEXT NOT NULL,
              createdBy TEXT,
              additionalData TEXT,
              syncStatus INTEGER DEFAULT 0
            )
          ''');
          debugPrint('Recreated agent_accounts table with all columns');
        } catch (e2) {
          debugPrint('Error recreating agent_accounts table: $e2');
        }
      }
    }

    // Add passwordHash field to users table (version 5)
    if (oldVersion < 5) {
      try {
        await db.execute('ALTER TABLE users ADD COLUMN passwordHash TEXT');
        debugPrint('Added passwordHash field to users table');
      } catch (e) {
        debugPrint('Error adding passwordHash field or it might already exist: $e');
        // For now, recreate all tables to ensure they have the correct structure
        await _recreateAllTables(db);
      }
    }

    // Add compositeImagePath to document_tracking and targetRole to notifications (version 6)
    if (oldVersion < 6) {
      try {
        // Add compositeImagePath to document_tracking table
        await db.execute('ALTER TABLE document_tracking ADD COLUMN compositeImagePath TEXT');
        debugPrint('Added compositeImagePath column to document_tracking table');
      } catch (e) {
        debugPrint('Error adding compositeImagePath column or it might already exist: $e');
      }

      try {
        // Add targetRole to notifications table
        await db.execute('ALTER TABLE notifications ADD COLUMN targetRole TEXT');
        debugPrint('Added targetRole column to notifications table');
      } catch (e) {
        debugPrint('Error adding targetRole column or it might already exist: $e');
      }

      try {
        // Add relatedId to notifications table
        await db.execute('ALTER TABLE notifications ADD COLUMN relatedId TEXT');
        debugPrint('Added relatedId column to notifications table');
      } catch (e) {
        debugPrint('Error adding relatedId column or it might already exist: $e');
      }

      try {
        // Add createdBy to notifications table
        await db.execute('ALTER TABLE notifications ADD COLUMN createdBy TEXT');
        debugPrint('Added createdBy column to notifications table');
      } catch (e) {
        debugPrint('Error adding createdBy column or it might already exist: $e');
      }
    }

    // Upgrade agent_payments table (version 7)
    if (oldVersion < 7) {
      try {
        // Check if agent_payments table exists
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='agent_payments'"
        );

        if (tables.isNotEmpty) {
          // Table exists, check for missing columns and add them
          final columns = await db.rawQuery('PRAGMA table_info(agent_payments)');
          final columnNames = columns.map((col) => col['name'] as String).toSet();

          if (!columnNames.contains('receiptNumber')) {
            await db.execute('ALTER TABLE agent_payments ADD COLUMN receiptNumber TEXT');
            if (kDebugMode) print('✅ Added receiptNumber column to agent_payments');
          }

          if (!columnNames.contains('paymentMethod')) {
            await db.execute('ALTER TABLE agent_payments ADD COLUMN paymentMethod TEXT DEFAULT "cash"');
            if (kDebugMode) print('✅ Added paymentMethod column to agent_payments');
          }

          if (!columnNames.contains('paymentDate')) {
            await db.execute('ALTER TABLE agent_payments ADD COLUMN paymentDate TEXT');
            if (kDebugMode) print('✅ Added paymentDate column to agent_payments');
          }

          if (!columnNames.contains('updatedAt')) {
            await db.execute('ALTER TABLE agent_payments ADD COLUMN updatedAt TEXT');
            if (kDebugMode) print('✅ Added updatedAt column to agent_payments');
          }
        } else {
          // Table doesn't exist, create it with all columns
          await db.execute('''
            CREATE TABLE IF NOT EXISTS agent_payments (
              id TEXT PRIMARY KEY,
              receiptNumber TEXT,
              agentId TEXT NOT NULL,
              amount REAL NOT NULL,
              paymentMethod TEXT DEFAULT 'cash',
              paymentDate TEXT,
              notes TEXT,
              createdAt TEXT NOT NULL,
              updatedAt TEXT,
              createdBy TEXT NOT NULL,
              status TEXT NOT NULL DEFAULT 'confirmed',
              syncStatus INTEGER DEFAULT 0
            )
          ''');
          if (kDebugMode) print('✅ Created agent_payments table with all columns');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error upgrading agent_payments table: $e');
        }
      }
    }

    // Fix inventory_movements table schema (version 8)
    if (oldVersion < 8) {
      try {
        if (kDebugMode) {
          print('🔧 Upgrading inventory_movements table to version 8...');
        }

        // Check if inventory_movements table exists
        final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_movements'"
        );

        if (tables.isNotEmpty) {
          // Check current columns
          final columns = await db.rawQuery('PRAGMA table_info(inventory_movements)');
          final columnNames = columns.map((col) => col['name'] as String).toSet();

          // Add missing columns
          if (!columnNames.contains('brand')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN brand TEXT');
            if (kDebugMode) print('✅ Added brand column to inventory_movements');
          }

          if (!columnNames.contains('model')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN model TEXT');
            if (kDebugMode) print('✅ Added model column to inventory_movements');
          }

          if (!columnNames.contains('sourceWarehouseName')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN sourceWarehouseName TEXT');
            if (kDebugMode) print('✅ Added sourceWarehouseName column to inventory_movements');
          }

          if (!columnNames.contains('targetWarehouseName')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN targetWarehouseName TEXT');
            if (kDebugMode) print('✅ Added targetWarehouseName column to inventory_movements');
          }

          if (!columnNames.contains('referenceId')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN referenceId TEXT');
            if (kDebugMode) print('✅ Added referenceId column to inventory_movements');
          }

          if (!columnNames.contains('unitCost')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN unitCost REAL');
            if (kDebugMode) print('✅ Added unitCost column to inventory_movements');
          }

          if (!columnNames.contains('totalCost')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN totalCost REAL');
            if (kDebugMode) print('✅ Added totalCost column to inventory_movements');
          }

          if (!columnNames.contains('createdBy')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN createdBy TEXT');
            if (kDebugMode) print('✅ Added createdBy column to inventory_movements');
          }

          if (!columnNames.contains('notes')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN notes TEXT');
            if (kDebugMode) print('✅ Added notes column to inventory_movements');
          }

          if (!columnNames.contains('additionalData')) {
            await db.execute('ALTER TABLE inventory_movements ADD COLUMN additionalData TEXT');
            if (kDebugMode) print('✅ Added additionalData column to inventory_movements');
          }

          // Handle performedBy to createdBy migration
          if (columnNames.contains('performedBy')) {
            if (!columnNames.contains('createdBy')) {
              // Add createdBy column
              await db.execute('ALTER TABLE inventory_movements ADD COLUMN createdBy TEXT');
              if (kDebugMode) print('✅ Added createdBy column to inventory_movements');
            }

            // Copy data from performedBy to createdBy
            await db.execute('UPDATE inventory_movements SET createdBy = performedBy WHERE createdBy IS NULL OR createdBy = ""');
            if (kDebugMode) print('✅ Migrated performedBy data to createdBy column');

            // We can't drop the performedBy column in SQLite, but we can ignore it
            if (kDebugMode) print('ℹ️ performedBy column kept for compatibility (will be ignored)');
          }

        } else {
          // Table doesn't exist, create it with correct schema
          await db.execute('''
            CREATE TABLE inventory_movements (
              id TEXT PRIMARY KEY,
              itemId TEXT NOT NULL,
              itemName TEXT NOT NULL,
              brand TEXT,
              model TEXT,
              movementType TEXT NOT NULL,
              quantity INTEGER NOT NULL,
              sourceWarehouseId TEXT NOT NULL,
              targetWarehouseId TEXT,
              sourceWarehouseName TEXT,
              targetWarehouseName TEXT,
              reason TEXT,
              referenceId TEXT,
              unitCost REAL,
              totalCost REAL,
              timestamp TEXT NOT NULL,
              createdBy TEXT NOT NULL,
              notes TEXT,
              additionalData TEXT,
              syncStatus INTEGER DEFAULT 0
            )
          ''');
          if (kDebugMode) print('✅ Created inventory_movements table with correct schema');
        }

        // Final check: ensure the table has the correct structure
        final finalColumns = await db.rawQuery('PRAGMA table_info(inventory_movements)');
        final finalColumnNames = finalColumns.map((col) => col['name'] as String).toSet();

        if (kDebugMode) {
          print('✅ Final inventory_movements columns: ${finalColumnNames.join(', ')}');
        }

        // If we still have performedBy but no createdBy, recreate the table
        if (finalColumnNames.contains('performedBy') && !finalColumnNames.contains('createdBy')) {
          if (kDebugMode) print('🔄 Recreating inventory_movements table due to schema conflict...');

          // Backup existing data
          final existingData = await db.rawQuery('SELECT * FROM inventory_movements');

          // Drop and recreate table
          await db.execute('DROP TABLE inventory_movements');
          await db.execute('''
            CREATE TABLE inventory_movements (
              id TEXT PRIMARY KEY,
              itemId TEXT NOT NULL,
              itemName TEXT NOT NULL,
              brand TEXT,
              model TEXT,
              movementType TEXT NOT NULL,
              quantity INTEGER NOT NULL,
              sourceWarehouseId TEXT NOT NULL,
              targetWarehouseId TEXT,
              sourceWarehouseName TEXT,
              targetWarehouseName TEXT,
              reason TEXT,
              referenceId TEXT,
              unitCost REAL,
              totalCost REAL,
              timestamp TEXT NOT NULL,
              createdBy TEXT NOT NULL,
              notes TEXT,
              additionalData TEXT,
              syncStatus INTEGER DEFAULT 0
            )
          ''');

          // Restore data with column mapping
          for (final row in existingData) {
            try {
              await db.execute('''
                INSERT INTO inventory_movements (
                  id, itemId, itemName, brand, model, movementType, quantity,
                  sourceWarehouseId, targetWarehouseId, sourceWarehouseName, targetWarehouseName,
                  reason, referenceId, timestamp, createdBy, syncStatus
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              ''', [
                row['id'],
                row['itemId'],
                row['itemName'],
                row['brand'] ?? '',
                row['model'] ?? '',
                row['movementType'],
                row['quantity'],
                row['sourceWarehouseId'],
                row['targetWarehouseId'],
                row['sourceWarehouseName'] ?? '',
                row['targetWarehouseName'] ?? '',
                row['reason'] ?? '',
                row['referenceId'] ?? '',
                row['timestamp'],
                row['performedBy'] ?? row['createdBy'] ?? 'unknown', // Use performedBy as createdBy
                row['syncStatus'] ?? 0,
              ]);
            } catch (e) {
              if (kDebugMode) print('⚠️ Failed to restore row ${row['id']}: $e');
            }
          }

          if (kDebugMode) print('✅ inventory_movements table recreated successfully');
        }

        if (kDebugMode) {
          print('✅ inventory_movements table upgrade completed');
        }

      } catch (e) {
        if (kDebugMode) {
          print('❌ Error upgrading inventory_movements table: $e');
        }
      }
    }
  }

  Future<void> _recreateAllTables(Database db) async {
    try {
      // Drop existing tables
      await db.execute('DROP TABLE IF EXISTS users');
      await db.execute('DROP TABLE IF EXISTS warehouses');
      await db.execute('DROP TABLE IF EXISTS items');
      await db.execute('DROP TABLE IF EXISTS invoices');
      await db.execute('DROP TABLE IF EXISTS payments');
      await db.execute('DROP TABLE IF EXISTS document_tracking');
      await db.execute('DROP TABLE IF EXISTS sync_queue');
      await db.execute('DROP TABLE IF EXISTS demo_passwords');
      await db.execute('DROP TABLE IF EXISTS settings');
      await db.execute('DROP TABLE IF EXISTS agent_accounts');
      await db.execute('DROP TABLE IF EXISTS agent_transfer_invoices');
      await db.execute('DROP TABLE IF EXISTS inventory_movements');
      await db.execute('DROP TABLE IF EXISTS warehouse_items');
      await db.execute('DROP TABLE IF EXISTS notifications');

      // Recreate tables with current structure
      await _createTables(db, 1);

      if (kDebugMode) {
        print('All tables recreated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error recreating tables: $e');
      }
      rethrow;
    }
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    try {
      final db = await database;

      // Create a copy of the data to avoid modifying the original
      final cleanedData = Map<String, dynamic>.from(data);

      // Convert complex data types to JSON strings for SQLite compatibility
      cleanedData.forEach((key, value) {
        if (value != null) {
          if (value is Map || value is List) {
            cleanedData[key] = jsonEncode(value);
          } else if (value is bool) {
            cleanedData[key] = value ? 1 : 0; // Convert bool to int
          }
        }
      });

      // Only add syncStatus if not already present
      if (!cleanedData.containsKey('syncStatus')) {
        cleanedData['syncStatus'] = 0; // Mark as not synced
      }

      return await db.insert(table, cleanedData, conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      if (kDebugMode) {
        print('Error inserting into $table: $e');
      }
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      return await db.query(
        table,
        where: where,
        whereArgs: whereArgs,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error querying $table: $e');
      }
      rethrow;
    }
  }

  Future<int> update(String table, Map<String, dynamic> data, String where, List<dynamic> whereArgs) async {
    try {
      final db = await database;
      data['syncStatus'] = 0; // Mark as not synced
      return await db.update(table, data, where: where, whereArgs: whereArgs);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating $table: $e');
      }
      rethrow;
    }
  }

  Future<int> delete(String table, String where, List<dynamic> whereArgs) async {
    try {
      final db = await database;
      return await db.delete(table, where: where, whereArgs: whereArgs);
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting from $table: $e');
      }
      rethrow;
    }
  }

  // Sync queue operations
  Future<void> addToSyncQueue(String tableName, String recordId, String operation, Map<String, dynamic> data) async {
    try {
      final db = await database;
      await db.insert('sync_queue', {
        'tableName': tableName,
        'recordId': recordId,
        'operation': operation,
        'data': data.toString(),
        'createdAt': DateTime.now().toIso8601String(),
        'retryCount': 0,
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error adding to sync queue: $e');
      }
    }
  }

  Future<List<Map<String, dynamic>>> getPendingSyncItems() async {
    try {
      final db = await database;
      return await db.query('sync_queue', orderBy: 'createdAt ASC');
    } catch (e) {
      if (kDebugMode) {
        print('Error getting pending sync items: $e');
      }
      return [];
    }
  }

  Future<void> removeSyncItem(int syncId) async {
    try {
      final db = await database;
      await db.delete('sync_queue', where: 'id = ?', whereArgs: [syncId]);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing sync item: $e');
      }
    }
  }

  Future<void> markAsSynced(String table, String recordId) async {
    try {
      final db = await database;
      await db.update(
        table,
        {'syncStatus': 1},
        where: 'id = ?',
        whereArgs: [recordId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error marking as synced: $e');
      }
    }
  }

  // Clear all data (for testing or reset)
  Future<void> clearAllData() async {
    try {
      final db = await database;
      await db.delete('users');
      await db.delete('warehouses');
      await db.delete('items');
      await db.delete('invoices');
      await db.delete('payments');
      await db.delete('document_tracking');
      await db.delete('sync_queue');
      await db.delete('agent_accounts');
      await db.delete('agent_transfer_invoices');
      await db.delete('inventory_movements');
      await db.delete('warehouse_items');
      await db.delete('notifications');

      if (kDebugMode) {
        print('All data cleared from local database');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing data: $e');
      }
      rethrow;
    }
  }

  // Clear demo data only (keep admin user)
  Future<void> clearDemoData() async {
    try {
      final db = await database;

      // Clear all tables except users (keep admin user)
      await db.delete('warehouses');
      await db.delete('items');
      await db.delete('invoices');
      await db.delete('payments');
      await db.delete('document_tracking');
      await db.delete('agent_accounts');
      await db.delete('agent_transfer_invoices');
      await db.delete('inventory_movements');
      await db.delete('warehouse_items');
      await db.delete('notifications');



      // Clear sync queue
      await db.delete('sync_queue');

      if (kDebugMode) {
        print('Demo data cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing demo data: $e');
      }
      rethrow;
    }
  }

  // User management methods
  Future<String> createUser(Map<String, dynamic> userData) async {
    final db = await database;
    final id = userData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString();
    userData['id'] = id;

    await db.insert('users', userData);
    return id;
  }

  Future<void> updateWarehouse(String warehouseId, Map<String, dynamic> updates) async {
    final db = await database;
    await db.update(
      'warehouses',
      updates,
      where: 'id = ?',
      whereArgs: [warehouseId],
    );
  }

  Future<void> deleteUser(String userId) async {
    final db = await database;
    await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  Future<List<Map<String, dynamic>>> getAllUsers() async {
    final db = await database;
    return await db.query('users');
  }

  Future<void> createWarehouse(Map<String, dynamic> warehouseData) async {
    final db = await database;
    await db.insert('warehouses', warehouseData);
  }

  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    final db = await database;
    await db.update(
      'users',
      updates,
      where: 'id = ?',
      whereArgs: [userId],
    );
  }

  // Close database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
