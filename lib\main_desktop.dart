import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import 'core/theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'services/data_service.dart';

import 'screens/desktop/full_desktop_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🖥️ Starting desktop app...');
  }

  runApp(const ElFarhanDesktopApp());
}

class ElFarhanDesktopApp extends StatelessWidget {
  const ElFarhanDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'الفرحان للنقل الخفيف - سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              // Desktop-specific theme adjustments
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            darkTheme: AppTheme.darkTheme,
            home: const FullDesktopApp(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
