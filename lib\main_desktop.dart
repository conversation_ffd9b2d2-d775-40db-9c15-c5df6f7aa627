import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'dart:io' show Platform;

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'services/local_database_service.dart';
import 'providers/auth_provider.dart';
import 'services/firebase_service.dart';
import 'services/data_service.dart';
import 'services/enhanced_pdf_service.dart';

import 'screens/splash/splash_screen.dart';
import 'core/performance/performance_monitor.dart';
import 'core/routes/app_routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services for desktop
  await _initializeDesktopServices();

  // Initialize performance monitoring
  PerformanceMonitor.instance.setEnabled(kDebugMode);
  PerformanceMonitor.instance.monitorAppLifecycle();

  runApp(const ElFarhanDesktopApp());
}

Future<void> _initializeDesktopServices() async {
  try {
    if (kDebugMode) {
      print('🖥️ Starting desktop app initialization...');
    }

    // Initialize only essential services for desktop
    await FirebaseService.instance.initialize();
    await LocalDatabaseService.instance.initialize();
    
    // Initialize PDF service for desktop
    await EnhancedPdfService.instance.initialize();

    if (kDebugMode) {
      print('✅ Desktop services initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing desktop services: $e');
    }
    rethrow;
  }
}

class ElFarhanDesktopApp extends StatelessWidget {
  const ElFarhanDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: '${AppConstants.appName} - سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              // Desktop-specific theme adjustments
              visualDensity: VisualDensity.adaptivePlatformDensity,
              useMaterial3: true,
            ),
            darkTheme: AppTheme.darkTheme,
            home: const SplashScreen(),
            onGenerateRoute: AppRoutes.generateRoute,
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
