import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';

import 'core/theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'services/data_service.dart';
import 'services/local_database_service.dart';
import 'services/enhanced_notification_service.dart';
import 'services/enhanced_pdf_service.dart';
import 'services/auto_sync_service.dart';
import 'services/firebase_service.dart';

import 'screens/desktop/full_desktop_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🖥️ Starting El Farhan Desktop App...');
  }

  // Initialize window manager for desktop
  await windowManager.ensureInitialized();

  // Configure window
  WindowOptions windowOptions = const WindowOptions(
    size: Size(1400, 900),
    minimumSize: Size(1200, 800),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    title: 'الفرحان للنقل الخفيف - نسخة سطح المكتب',
  );

  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });

  // Initialize services
  await _initializeServices();

  runApp(const ElFarhanDesktopApp());
}

Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔄 Initializing services...');
    }

    // Initialize local database first
    await LocalDatabaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ Local database initialized');
    }

    // Initialize Firebase (with error handling for desktop)
    try {
      await FirebaseService.instance.initialize();
      if (kDebugMode) {
        print('✅ Firebase initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firebase initialization failed (desktop mode): $e');
        print('📱 Running in offline mode');
      }
    }

    // Data service is initialized automatically
    if (kDebugMode) {
      print('✅ Data service ready');
    }

    // Initialize notification service
    await EnhancedNotificationService.instance.initialize();
    if (kDebugMode) {
      print('✅ Notification service initialized');
    }

    // Initialize PDF service
    await EnhancedPdfService.instance.initialize();
    if (kDebugMode) {
      print('✅ PDF service initialized');
    }

    // Initialize auto sync service
    await AutoSyncService.instance.initialize();
    if (kDebugMode) {
      print('✅ Auto sync service initialized');
    }

    if (kDebugMode) {
      print('🎉 All services initialized successfully!');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing services: $e');
    }
  }
}

class ElFarhanDesktopApp extends StatelessWidget {
  const ElFarhanDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'الفرحان للنقل الخفيف - سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              // Desktop-specific theme adjustments
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            darkTheme: AppTheme.darkTheme,
            home: const FullDesktopApp(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}
