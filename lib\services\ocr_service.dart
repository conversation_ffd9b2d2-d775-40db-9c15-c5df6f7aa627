import 'package:flutter/foundation.dart';

class OCRService {
  static final OCRService _instance = OCRService._internal();
  factory OCRService() => _instance;
  OCRService._internal();

  static OCRService get instance => _instance;

  Future<OCRResult> extractTextFromImage(dynamic imageFile) async {
    debugPrint('OCR not supported in desktop mode');
    return OCRResult(
      text: '',
      confidence: 0.0,
      isChassisNumber: false,
      isMotorFingerprint: false,
      isIdCard: false,
      extractedData: {},
      blocks: [],
    );
  }

  Future<Map<String, String>> extractMotorFingerprintData(dynamic imageFile) async {
    return {};
  }

  Future<Map<String, String>> extractIdCardData(dynamic frontImage, dynamic backImage) async {
    return {};
  }

  Future<String> extractChassisNumber(dynamic imageFile) async {
    return '';
  }

  bool isValidChassisNumber(String text) {
    if (text.isEmpty) return false;
    final cleanText = text.replaceAll(' ', '').toUpperCase();
    if (cleanText.length == 17) {
      return RegExp(r'^[A-HJ-NPR-Z0-9]{17}$').hasMatch(cleanText);
    }
    if (cleanText.length >= 8 && cleanText.length <= 12) {
      return RegExp(r'^[A-Z0-9]+$').hasMatch(cleanText);
    }
    return false;
  }

  bool isValidMotorFingerprint(String text) {
    if (text.isEmpty) return false;
    final cleanText = text.replaceAll(' ', '').toUpperCase();
    final hasLetters = RegExp(r'[A-Z]').hasMatch(cleanText);
    final hasNumbers = RegExp(r'[0-9]').hasMatch(cleanText);
    return hasLetters && hasNumbers && cleanText.length >= 6;
  }

  String cleanExtractedText(String text) {
    return text
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim()
        .toUpperCase();
  }

  void dispose() {
    debugPrint('OCR service disposed');
  }
}

class OCRResult {
  final String text;
  final double confidence;
  final bool isMotorFingerprint;
  final bool isIdCard;
  final bool isChassisNumber;
  final Map<String, String> extractedData;
  final List<dynamic> blocks;

  OCRResult({
    required this.text,
    required this.confidence,
    required this.isMotorFingerprint,
    required this.isIdCard,
    required this.isChassisNumber,
    required this.extractedData,
    required this.blocks,
  });

  @override
  String toString() {
    return 'OCRResult(text: ${text.length} chars, confidence: ${confidence.toStringAsFixed(2)}, '
           'isMotorFingerprint: $isMotorFingerprint, isIdCard: $isIdCard, isChassisNumber: $isChassisNumber)';
  }
}

class TextAnalysis {
  bool isMotorFingerprint = false;
  bool isIdCard = false;
  bool isChassisNumber = false;
  Map<String, String> extractedData = {};
}
