import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';

import 'core/theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'services/local_database_service.dart';
import 'services/data_service.dart';

import 'screens/desktop/full_desktop_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🖥️ Starting El Farhan Desktop App (Simple Mode)...');
  }

  // Initialize window manager for desktop
  await windowManager.ensureInitialized();

  // Set high DPI awareness
  if (kDebugMode) {
    print('🔧 Configuring window settings...');
  }

  // Configure window
  WindowOptions windowOptions = const WindowOptions(
    size: Size(1400, 900),
    minimumSize: Size(1200, 800),
    center: true,
    backgroundColor: Colors.white,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    title: 'الفرحان للنقل الخفيف - نسخة سطح المكتب',
    windowButtonVisibility: true,
  );

  await windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
    await windowManager.setPreventClose(false);
    await windowManager.setSkipTaskbar(false);
  });

  // Initialize services (without Firebase)
  await _initializeServices();

  runApp(const ElFarhanDesktopApp());
}

Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔄 Initializing services (offline mode)...');
    }

    // Initialize local database first
    await LocalDatabaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ Local database initialized');
    }

    // Skip Firebase initialization for desktop
    if (kDebugMode) {
      print('⚠️ Firebase skipped (desktop offline mode)');
    }

    // Data service is ready
    if (kDebugMode) {
      print('✅ Data service ready');
    }

    if (kDebugMode) {
      print('🎉 All services initialized successfully (offline mode)!');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing services: $e');
    }
  }
}

class ElFarhanDesktopApp extends StatelessWidget {
  const ElFarhanDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'الفرحان للنقل الخفيف - نسخة سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              // Desktop-specific theme adjustments
              visualDensity: VisualDensity.adaptivePlatformDensity,
              // Better desktop colors
              colorScheme: AppTheme.lightTheme.colorScheme.copyWith(
                primary: Colors.blue.shade800,
                secondary: Colors.blue.shade600,
              ),
            ),
            darkTheme: AppTheme.darkTheme,
            home: const FullDesktopApp(),
            debugShowCheckedModeBanner: false,
            // Desktop-specific configurations
            scrollBehavior: const MaterialScrollBehavior(),
          );
        },
      ),
    );
  }
}
