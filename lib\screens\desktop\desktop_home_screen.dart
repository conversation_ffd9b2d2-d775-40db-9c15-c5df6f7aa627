import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_utils.dart';
import '../../utils/constants.dart';
import '../inventory/inventory_screen.dart';
import '../sales/sales_screen.dart';
import '../agents/agents_screen.dart';
import '../warehouses/warehouses_screen.dart';
import '../reports/reports_screen.dart';
// Simplified imports for desktop
// import '../notifications/advanced_notifications_screen.dart';
// import '../document_tracking/document_tracking_screen.dart';
// import '../settings/settings_screen.dart';
// import '../users/user_management_screen.dart';

class DesktopHomeScreen extends StatefulWidget {
  const DesktopHomeScreen({super.key});

  @override
  State<DesktopHomeScreen> createState() => _DesktopHomeScreenState();
}

class _DesktopHomeScreenState extends State<DesktopHomeScreen> {
  int _selectedIndex = 0;
  bool _isCollapsed = false;

  final List<DesktopMenuItem> _menuItems = [
    DesktopMenuItem(
      icon: Icons.dashboard,
      title: 'لوحة التحكم',
      screen: const DesktopDashboardWidget(),
    ),
    DesktopMenuItem(
      icon: Icons.inventory,
      title: 'المخزون',
      screen: const InventoryScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.point_of_sale,
      title: 'المبيعات',
      screen: const SalesScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.people,
      title: 'الوكلاء',
      screen: const DesktopPlaceholderScreen(title: 'الوكلاء'),
      requiredRoles: ['admin', 'super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.warehouse,
      title: 'المخازن',
      screen: const DesktopPlaceholderScreen(title: 'المخازن'),
      requiredRoles: ['admin', 'super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.description,
      title: 'تتبع الجوابات',
      screen: const DesktopPlaceholderScreen(title: 'تتبع الجوابات'),
    ),
    DesktopMenuItem(
      icon: Icons.bar_chart,
      title: 'التقارير',
      screen: const ReportsScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.notifications,
      title: 'الإشعارات',
      screen: const DesktopPlaceholderScreen(title: 'الإشعارات'),
    ),
    DesktopMenuItem(
      icon: Icons.manage_accounts,
      title: 'إدارة المستخدمين',
      screen: const DesktopPlaceholderScreen(title: 'إدارة المستخدمين'),
      requiredRoles: ['super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.settings,
      title: 'الإعدادات',
      screen: const DesktopPlaceholderScreen(title: 'الإعدادات'),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;
        if (currentUser == null) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        // Filter menu items based on user role
        final filteredMenuItems = _menuItems.where((item) {
          if (item.requiredRoles == null) return true;
          return item.requiredRoles!.contains(currentUser.role);
        }).toList();

        return Scaffold(
          body: Row(
            children: [
              // Sidebar Navigation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: _isCollapsed ? 70 : 280,
                child: _buildSidebar(currentUser, filteredMenuItems),
              ),
              
              // Main Content Area
              Expanded(
                child: Column(
                  children: [
                    // Top App Bar
                    _buildTopAppBar(currentUser),
                    
                    // Content
                    Expanded(
                      child: Container(
                        color: Colors.grey[50],
                        child: filteredMenuItems[_selectedIndex].screen,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSidebar(UserModel currentUser, List<DesktopMenuItem> menuItems) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Logo and Company Name
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.local_shipping,
                    color: AppColors.primary,
                    size: 24,
                  ),
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'الفرحان للنقل الخفيف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          const Divider(color: Colors.white24, height: 1),
          
          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final item = menuItems[index];
                final isSelected = _selectedIndex == index;
                
                return _buildMenuItem(item, index, isSelected);
              },
            ),
          ),
          
          // Collapse Button
          Container(
            padding: const EdgeInsets.all(8),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _isCollapsed = !_isCollapsed;
                });
              },
              icon: Icon(
                _isCollapsed ? Icons.menu : Icons.menu_open,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(DesktopMenuItem item, int index, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _selectedIndex = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  color: Colors.white,
                  size: 20,
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      item.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppBar(UserModel currentUser) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const SizedBox(width: 20),
          
          // Page Title
          Text(
            _menuItems[_selectedIndex].title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          
          const Spacer(),
          
          // User Info and Actions
          Row(
            children: [
              // Notifications
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedIndex = _menuItems.indexWhere(
                      (item) => item.title == 'الإشعارات',
                    );
                  });
                },
                icon: const Icon(Icons.notifications_outlined),
              ),
              
              const SizedBox(width: 16),
              
              // User Profile
              PopupMenuButton<String>(
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: AppColors.primary,
                      child: Text(
                        currentUser.fullName.isNotEmpty 
                          ? currentUser.fullName[0].toUpperCase()
                          : 'U',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentUser.fullName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          AppUtils.getRoleDisplayName(currentUser.role),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        Icon(Icons.person),
                        SizedBox(width: 8),
                        Text('الملف الشخصي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings),
                        SizedBox(width: 8),
                        Text('الإعدادات'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  switch (value) {
                    case 'settings':
                      setState(() {
                        _selectedIndex = _menuItems.indexWhere(
                          (item) => item.title == 'الإعدادات',
                        );
                      });
                      break;
                    case 'logout':
                      _handleLogout();
                      break;
                  }
                },
              ),
              
              const SizedBox(width: 20),
            ],
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<AuthProvider>(context, listen: false).logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}

class DesktopMenuItem {
  final IconData icon;
  final String title;
  final Widget screen;
  final List<String>? requiredRoles;

  DesktopMenuItem({
    required this.icon,
    required this.title,
    required this.screen,
    this.requiredRoles,
  });
}

class DesktopDashboardWidget extends StatelessWidget {
  const DesktopDashboardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك في نظام الفرحان للنقل الخفيف',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'نظام إدارة شامل للمخزون والمبيعات والوكلاء',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 32),
          // TODO: Add dashboard widgets here
          Expanded(
            child: Center(
              child: Text(
                'لوحة التحكم قيد التطوير...',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DesktopPlaceholderScreen extends StatelessWidget {
  final String title;

  const DesktopPlaceholderScreen({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'هذا القسم متاح في النسخة الكاملة للتطبيق',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 32),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.construction,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'قسم $title قيد التطوير',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'سيتم إضافة هذا القسم في التحديثات القادمة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
