import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import 'core/theme/app_theme.dart';
import 'providers/simple_auth_provider.dart';
import 'services/data_service.dart';
import 'screens/desktop/full_desktop_app.dart';
import 'screens/auth/simple_login_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🖥️ Starting simple desktop app...');
  }

  runApp(const SimpleElFarhanApp());
}

class SimpleElFarhanApp extends StatelessWidget {
  const SimpleElFarhanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SimpleAuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<SimpleAuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'الفرحان للنقل الخفيف - سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              visualDensity: VisualDensity.adaptivePlatformDensity,
            ),
            darkTheme: AppTheme.darkTheme,
            home: _buildResponsiveHome(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }

  Widget _buildResponsiveHome() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // If screen width is greater than 800px, show desktop version
        if (constraints.maxWidth > 800) {
          return const FullDesktopApp();
        } else {
          // For smaller screens, show login
          return const SimpleLoginScreen();
        }
      },
    );
  }
}
