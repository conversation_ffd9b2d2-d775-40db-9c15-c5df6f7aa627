import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../providers/auth_provider.dart';
import '../../services/local_database_service.dart';
import '../../services/data_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  String? _splashImagePath;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load splash image path from local storage
      final settings = await _localDb.query('settings', where: 'key = ?', whereArgs: ['splash_image']);
      if (settings.isNotEmpty) {
        setState(() {
          _splashImagePath = settings.first['value'];
        });
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الإعدادات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User Profile Section
                  _buildProfileSection(authProvider),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // App Settings Section
                  _buildAppSettingsSection(authProvider),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Data Management Section
                  _buildDataManagementSection(),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // About Section
                  _buildAboutSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildProfileSection(AuthProvider authProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الملف الشخصي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Icon(
                    Icons.person,
                    size: 30,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authProvider.currentUser?.fullName ?? '',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        authProvider.userRoleDisplayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      Text(
                        authProvider.currentUser?.email ?? '',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppSettingsSection(AuthProvider authProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Splash Image Setting (Super Admin only)
            if (authProvider.isSuperAdmin) ...[
              ListTile(
                leading: const Icon(Icons.image),
                title: const Text('صورة بداية التطبيق'),
                subtitle: Text(_splashImagePath != null ? 'تم تعيين صورة مخصصة' : 'استخدام الصورة الافتراضية'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _showSplashImageOptions,
              ),
              const Divider(),
            ],
            
            // Theme Setting
            ListTile(
              leading: const Icon(Icons.palette),
              title: const Text('المظهر'),
              subtitle: const Text('فاتح'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                AppUtils.showSnackBar(context, 'هذه الميزة قيد التطوير');
              },
            ),
            
            const Divider(),
            
            // Language Setting
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('اللغة'),
              subtitle: const Text('العربية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                AppUtils.showSnackBar(context, 'هذه الميزة قيد التطوير');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة البيانات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            ListTile(
              leading: const Icon(Icons.sync),
              title: const Text('مزامنة البيانات'),
              subtitle: const Text('مزامنة البيانات مع الخادم'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                AppUtils.showSnackBar(context, 'هذه الميزة قيد التطوير');
              },
            ),
            
            const Divider(),

            ListTile(
              leading: const Icon(Icons.backup),
              title: const Text('نسخ احتياطي'),
              subtitle: const Text('إنشاء نسخة احتياطية من البيانات'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                AppUtils.showSnackBar(context, 'هذه الميزة قيد التطوير');
              },
            ),

            const Divider(),

            ListTile(
              leading: const Icon(Icons.update),
              title: const Text('تحديث حالة الأصناف'),
              subtitle: const Text('تحديث حالة الأصناف من الإنجليزية للعربية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _updateItemStatuses,
            ),

            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.restore, color: Colors.orange),
              title: const Text('استعادة البيانات'),
              subtitle: const Text('استعادة البيانات من نسخة احتياطية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                AppUtils.showSnackBar(context, 'هذه الميزة قيد التطوير');
              },
            ),

            // Fix notifications table button (for admins only)
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                if (authProvider.isSuperAdmin || authProvider.isAdmin) {
                  return Column(
                    children: [
                      const Divider(),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.build, color: Colors.orange),
                          title: const Text(
                            'إصلاح جدول الإشعارات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                          subtitle: const Text(
                            'إصلاح مشاكل جدول الإشعارات وتحويل الأصناف\n🔧 إصلاح سريع للمشاكل التقنية',
                            style: TextStyle(fontSize: 12),
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios, color: Colors.orange),
                          onTap: _fixNotificationsTable,
                        ),
                      ),

                      const SizedBox(height: 8),

                      Container(
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.cleaning_services, color: Colors.red),
                          title: const Text(
                            'تنظيف شامل للبيانات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          subtitle: const Text(
                            'حذف جميع البيانات المحلية وإعادة التزامن من Firebase\n⚠️ إجراء متقدم للمديرين فقط',
                            style: TextStyle(fontSize: 12),
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                          onTap: _showCompleteCleanupDialog,
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            const ListTile(
              leading: Icon(Icons.info),
              title: Text('الإصدار'),
              subtitle: Text('1.0.0'),
            ),
            
            const Divider(),

            // Debug section - only show in debug mode
            if (kDebugMode) ...[
              ListTile(
                leading: const Icon(Icons.refresh, color: Colors.orange),
                title: const Text('إعادة إنشاء قاعدة البيانات'),
                subtitle: const Text('حذف وإعادة إنشاء قاعدة البيانات المحلية'),
                onTap: _recreateDatabase,
              ),
              const Divider(),
            ],

            const ListTile(
              leading: Icon(Icons.developer_mode),
              title: Text('المطور'),
              subtitle: Text('معتصم سالم'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSplashImageOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickSplashImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickSplashImage(ImageSource.gallery);
              },
            ),
            if (_splashImagePath != null)
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('حذف الصورة المخصصة'),
                onTap: () {
                  Navigator.pop(context);
                  _removeSplashImage();
                },
              ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('إلغاء'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickSplashImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: source);
      
      if (image != null) {
        // Save image path to settings
        await _saveSplashImagePath(image.path);
        setState(() {
          _splashImagePath = image.path;
        });
        
        if (mounted) {
          AppUtils.showSnackBar(context, 'تم تحديث صورة بداية التطبيق');
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في اختيار الصورة: $e', isError: true);
      }
    }
  }

  Future<void> _removeSplashImage() async {
    try {
      await _localDb.delete('settings', 'key = ?', ['splash_image']);
      setState(() {
        _splashImagePath = null;
      });
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم حذف الصورة المخصصة');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في حذف الصورة: $e', isError: true);
      }
    }
  }

  Future<void> _saveSplashImagePath(String path) async {
    try {
      // Check if setting exists
      final existing = await _localDb.query('settings', where: 'key = ?', whereArgs: ['splash_image']);
      
      if (existing.isNotEmpty) {
        await _localDb.update('settings', {'value': path}, 'key = ?', ['splash_image']);
      } else {
        await _localDb.insert('settings', {'key': 'splash_image', 'value': path});
      }
    } catch (e) {
      throw 'خطأ في حفظ مسار الصورة: $e';
    }
  }

  /// Show complete cleanup confirmation dialog
  Future<void> _showCompleteCleanupDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('تحذير: تنظيف شامل'),
          ],
        ),
        content: const Text(
          '⚠️ هذا الإجراء سيقوم بـ:\n\n'
          '• حذف جميع البيانات المحلية\n'
          '• إعادة تحميل البيانات من Firebase\n'
          '• حذف التقارير والإشعارات المحلية\n'
          '• تنظيف ذاكرة التخزين المؤقت\n\n'
          '⚠️ تأكد من وجود اتصال قوي بالإنترنت\n\n'
          'هل تريد المتابعة؟',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تنظيف شامل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _performCompleteCleanup();
    }
  }

  /// Perform complete database cleanup
  Future<void> _performCompleteCleanup() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري تنظيف البيانات...'),
              Text('قد يستغرق هذا بضع دقائق'),
            ],
          ),
        ),
      );

      // Perform cleanup
      await DataService.instance.completeCleanupAndSync();

      // Close progress dialog
      if (mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تنظيف البيانات وإعادة التزامن بنجاح ✅'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Reload settings
        await _loadSettings();
      }
    } catch (e) {
      // Close progress dialog
      if (mounted) {
        Navigator.of(context).pop();

        AppUtils.showSnackBar(
          context,
          'فشل في تنظيف البيانات: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Fix notifications table schema
  Future<void> _fixNotificationsTable() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Show progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('جاري إصلاح جدول الإشعارات...'),
              Text('قد يستغرق هذا بضع ثوان'),
            ],
          ),
        ),
      );

      // Perform fix
      await DataService.instance.fixNotificationsTable();

      // Close progress dialog
      if (mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إصلاح جدول الإشعارات بنجاح ✅'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Close progress dialog
      if (mounted) {
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إصلاح جدول الإشعارات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateItemStatuses() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await DataService.instance.updateItemStatusesToArabic();

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث حالة الأصناف بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث حالة الأصناف: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _recreateDatabase() async {
    if (!kDebugMode) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة إنشاء قاعدة البيانات'),
        content: const Text(
          'هذا سيحذف جميع البيانات المحلية ويعيد إنشاء قاعدة البيانات.\n'
          'سيتم إعادة تحميل البيانات من Firebase.\n\n'
          'هل أنت متأكد؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      setState(() {
        _isLoading = true;
      });

      if (kDebugMode) {
        print('🔄 Starting database recreation...');
      }

      // Clear all data from the database
      await _localDb.clearAllData();

      if (kDebugMode) {
        print('✅ Database cleared successfully');
      }

      // Close and reinitialize the database (this will trigger schema upgrade)
      await _localDb.close();
      await _localDb.initialize();

      if (kDebugMode) {
        print('✅ Database recreated with new schema');
      }

      // Force sync from Firebase
      await DataService.instance.getUsers(forceFromFirebase: true);
      await DataService.instance.getWarehouses(forceFromFirebase: true);
      await DataService.instance.getItems(forceFromFirebase: true);

      if (kDebugMode) {
        print('✅ Data synced from Firebase');
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إعادة إنشاء قاعدة البيانات بنجاح');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recreating database: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إعادة إنشاء قاعدة البيانات: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
