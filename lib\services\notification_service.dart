import 'package:flutter/foundation.dart';
import '../core/utils/app_utils.dart';
import '../models/notification_model.dart';
import '../models/invoice_model.dart';
import '../models/user_model.dart';
import 'data_service.dart';

/// خدمة إدارة الإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  final DataService _dataService = DataService.instance;

  /// إرسال إشعار للمديرين عند بيع الوكيل لعميل
  Future<void> notifyManagersOnAgentSale({
    required InvoiceModel invoice,
    required UserModel agent,
  }) async {
    try {
      if (kDebugMode) {
        print('📢 Sending notification to managers for agent sale');
        print('   Agent: ${agent.fullName}');
        print('   Invoice: ${invoice.invoiceNumber}');
        print('   Customer: ${invoice.customerName ?? 'غير محدد'}');
      }

      // الحصول على بيانات الصنف
      String itemDescription = 'صنف';
      try {
        final item = await _dataService.getItemById(invoice.itemId);
        if (item != null) {
          itemDescription = '${item.brand} ${item.model}';
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Could not fetch item details: $e');
        }
      }

      // الحصول على جميع المديرين
      final managers = await _dataService.getUsersByRole('admin');
      final superAdmins = await _dataService.getUsersByRole('super_admin');
      final allManagers = [...managers, ...superAdmins];

      if (allManagers.isEmpty) {
        if (kDebugMode) {
          print('⚠️ No managers found to notify');
        }
        return;
      }

      // إنشاء الإشعار
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'بيع جديد من الوكيل ${agent.fullName}',
        message: 'تم بيع $itemDescription للعميل ${invoice.customerName ?? 'غير محدد'}\n'
                'قيمة البيع: ${AppUtils.formatCurrency(invoice.sellingPrice)}\n'
                'رقم الفاتورة: ${invoice.invoiceNumber}',
        type: 'agent_sale',
        isRead: false,
        createdAt: DateTime.now(),
        createdBy: agent.id,
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'customerName': invoice.customerName,
          'sellingPrice': invoice.sellingPrice,
          'itemId': invoice.itemId,
          'itemDescription': itemDescription,
          'action': 'view_document_tracking', // الإجراء المطلوب
        },
      );

      // إرسال الإشعار لجميع المديرين
      for (final manager in allManagers) {
        final managerNotification = notification.copyWith(
          id: AppUtils.generateId(),
          targetUserId: manager.id,
        );

        await _dataService.createNotification(managerNotification);

        if (kDebugMode) {
          print('✅ Notification sent to manager: ${manager.fullName}');
        }
      }

      if (kDebugMode) {
        print('🎉 Successfully sent notifications to ${allManagers.length} managers');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending notifications to managers: $e');
      }
      // لا نرمي الخطأ لأن الإشعارات ليست حرجة
    }
  }

  /// إرسال إشعار للوكيل عند تحديث حالة الجواب
  Future<void> notifyAgentOnDocumentUpdate({
    required String agentId,
    required String invoiceNumber,
    required String documentStatus,
    required String customerName,
  }) async {
    try {
      if (kDebugMode) {
        print('📢 Sending document update notification to agent');
        print('   Agent ID: $agentId');
        print('   Invoice: $invoiceNumber');
        print('   Status: $documentStatus');
      }

      // الحصول على بيانات الوكيل
      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        if (kDebugMode) {
          print('⚠️ Agent not found: $agentId');
        }
        return;
      }

      // تحديد رسالة الحالة
      String statusMessage;
      switch (documentStatus) {
        case 'data_sent':
          statusMessage = 'تم إرسال البيانات للمصنع';
          break;
        case 'documents_sent':
          statusMessage = 'تم إرسال الأوراق للمصنع';
          break;
        case 'response_received':
          statusMessage = 'تم استلام الرد من المصنع';
          break;
        case 'delivered_to_agent':
          statusMessage = 'تم تسليم الجواب للوكيل';
          break;
        default:
          statusMessage = 'تم تحديث حالة الجواب';
      }

      // إنشاء الإشعار
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        targetUserId: agentId,
        title: 'تحديث حالة الجواب',
        message: '$statusMessage\n'
                'العميل: $customerName\n'
                'رقم الفاتورة: $invoiceNumber',
        type: 'document_update',
        isRead: false,
        createdAt: DateTime.now(),
        createdBy: 'system',
        data: {
          'invoiceNumber': invoiceNumber,
          'documentStatus': documentStatus,
          'customerName': customerName,
          'action': 'view_document_tracking',
        },
      );

      await _dataService.createNotification(notification);

      if (kDebugMode) {
        print('✅ Document update notification sent to agent: ${agent.fullName}');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending document update notification: $e');
      }
    }
  }

  /// إرسال إشعار عام
  Future<void> sendNotification({
    required String recipientId,
    required String title,
    required String message,
    String type = 'general',
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        targetUserId: recipientId,
        title: title,
        message: message,
        type: type,
        isRead: false,
        createdAt: DateTime.now(),
        createdBy: 'system',
        data: data,
      );

      await _dataService.createNotification(notification);

      if (kDebugMode) {
        print('✅ General notification sent to: $recipientId');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending general notification: $e');
      }
    }
  }

  // ملاحظة: الدوال التالية تحتاج لتنفيذ في DataService
  // يمكن استخدام الخدمات الموجودة بدلاً منها
}
