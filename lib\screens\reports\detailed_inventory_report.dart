import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../services/data_service.dart';
import '../../services/pdf_service.dart';
import '../../models/item_model.dart';

class DetailedInventoryReport extends StatefulWidget {
  const DetailedInventoryReport({super.key});

  @override
  State<DetailedInventoryReport> createState() => _DetailedInventoryReportState();
}

class _DetailedInventoryReportState extends State<DetailedInventoryReport> {
  final DataService _dataService = DataService.instance;
  final PdfService _pdfService = PdfService.instance;
  List<ItemModel> _items = [];
  bool _isLoading = false;
  String _selectedStatus = 'all';
  String _selectedWarehouse = 'all';

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  Future<void> _loadItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final items = await _dataService.getItems(
        status: _selectedStatus == 'all' ? null : _selectedStatus,
        warehouseId: _selectedWarehouse == 'all' ? null : _selectedWarehouse,
      );
      setState(() {
        _items = items;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المخزون المفصل'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _generatePDF,
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadItems,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              children: [
                Expanded(
                  child: DropdownButton<String>(
                    value: _selectedStatus,
                    isExpanded: true,
                    hint: const Text('حالة الصنف'),
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                      _loadItems();
                    },
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                      DropdownMenuItem(value: 'available', child: Text('متاح')),
                      DropdownMenuItem(value: 'sold', child: Text('مباع')),
                      DropdownMenuItem(value: 'transferred', child: Text('محول')),
                    ],
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: DropdownButton<String>(
                    value: _selectedWarehouse,
                    isExpanded: true,
                    hint: const Text('المخزن'),
                    onChanged: (value) {
                      setState(() {
                        _selectedWarehouse = value!;
                      });
                      _loadItems();
                    },
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع المخازن')),
                      DropdownMenuItem(value: 'warehouse_001', child: Text('مخزن القاهرة')),
                      DropdownMenuItem(value: 'warehouse_002', child: Text('مخزن الإسكندرية')),
                      DropdownMenuItem(value: 'warehouse_003', child: Text('معرض الجيزة')),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Summary
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryCard('إجمالي الأصناف', _items.length.toString(), Colors.blue),
                _buildSummaryCard('القيمة الإجمالية', 
                  '${_items.fold<double>(0, (sum, item) => sum + item.purchasePrice).toStringAsFixed(0)} ج.م', 
                  Colors.green),
                _buildSummaryCard('المتاح', 
                  _items.where((item) => item.status == 'available').length.toString(), 
                  Colors.orange),
              ],
            ),
          ),
          
          const Divider(),
          
          // Items table
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _items.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد أصناف',
                          style: TextStyle(fontSize: 18),
                        ),
                      )
                    : SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: DataTable(
                          columns: const [
                            DataColumn(label: Text('النوع')),
                            DataColumn(label: Text('الموديل')),
                            DataColumn(label: Text('اللون')),
                            DataColumn(label: Text('الماركة')),
                            DataColumn(label: Text('سنة الصنع')),
                            DataColumn(label: Text('سعر الشراء')),
                            DataColumn(label: Text('سعر البيع')),
                            DataColumn(label: Text('الحالة')),
                            DataColumn(label: Text('المخزن')),
                          ],
                          rows: _items.map((item) => DataRow(
                            cells: [
                              DataCell(Text(item.type)),
                              DataCell(Text(item.model)),
                              DataCell(Text(item.color)),
                              DataCell(Text(item.brand)),
                              DataCell(Text(item.yearOfManufacture.toString())),
                              DataCell(Text('${item.purchasePrice.toStringAsFixed(0)} ج.م')),
                              DataCell(Text('${item.suggestedSellingPrice.toStringAsFixed(0)} ج.م')),
                              DataCell(_buildStatusChip(item.status)),
                              DataCell(Text(_getWarehouseName(item.currentWarehouseId))),
                            ],
                          )).toList(),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        child: Column(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;
    
    switch (status) {
      case 'available':
      case 'متاح':
        color = Colors.green;
        label = 'متاح';
        break;
      case 'sold':
      case 'مباع':
        color = Colors.red;
        label = 'مباع';
        break;
      case 'transferred':
      case 'محول':
        color = Colors.orange;
        label = 'محول';
        break;
      case 'returned':
      case 'مرتجع':
        color = Colors.red;
        label = 'مرتجع';
        break;
      default:
        color = Colors.grey;
        label = status;
    }
    
    return Chip(
      label: Text(label),
      backgroundColor: color.withValues(alpha: 0.2),
      labelStyle: TextStyle(color: color, fontSize: 12),
    );
  }

  String _getWarehouseName(String warehouseId) {
    switch (warehouseId) {
      case 'warehouse_001':
        return 'مخزن القاهرة';
      case 'warehouse_002':
        return 'مخزن الإسكندرية';
      case 'warehouse_003':
        return 'معرض الجيزة';
      default:
        return 'غير محدد';
    }
  }

  Future<void> _generatePDF() async {
    try {
      final totalValue = _items.fold<double>(0, (sum, item) => sum + item.purchasePrice);
      final availableItems = _items.where((item) => item.status == 'available').length;

      final itemsData = _items.map((item) => {
        'type': item.type,
        'model': item.model,
        'color': item.color,
        'brand': item.brand,
        'yearOfManufacture': item.yearOfManufacture,
        'purchasePrice': item.purchasePrice,
        'suggestedSellingPrice': item.suggestedSellingPrice,
        'status': item.status,
        'currentWarehouseId': item.currentWarehouseId,
      }).toList();

      final summary = {
        'totalItems': _items.length,
        'totalValue': totalValue,
        'availableItems': availableItems,
      };

      final pdfData = await _pdfService.generateInventoryReportPdf(
        items: itemsData,
        title: 'تقرير المخزون المفصل',
        summary: summary,
      );

      await _pdfService.printPdf(pdfData, 'تقرير المخزون المفصل');
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
      }
    }
  }


}
