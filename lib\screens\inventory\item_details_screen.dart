import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/data_service.dart';
import '../../providers/auth_provider.dart';
import 'add_item_screen.dart';

class ItemDetailsScreen extends StatefulWidget {
  final ItemModel item;

  const ItemDetailsScreen({
    super.key,
    required this.item,
  });

  @override
  State<ItemDetailsScreen> createState() => _ItemDetailsScreenState();
}

class _ItemDetailsScreenState extends State<ItemDetailsScreen> {
  final DataService _dataService = DataService.instance;
  
  late ItemModel _item;
  WarehouseModel? _warehouse;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _item = widget.item;
    _loadWarehouse();
  }

  Future<void> _loadWarehouse() async {
    try {
      final warehouse = await _dataService.getWarehouseById(_item.currentWarehouseId);
      setState(() {
        _warehouse = warehouse;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل بيانات المخزن: $e', isError: true);
      }
    }
  }

  Future<void> _updateItemStatus(String newStatus) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final updatedItem = _item.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      await _dataService.updateItem(updatedItem);
      
      setState(() {
        _item = updatedItem;
      });

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث حالة الصنف بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث حالة الصنف: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showStatusUpdateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الصنف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatusOption('available', 'متاح'),
            _buildStatusOption('transferred', 'محول'),
            _buildStatusOption('returned', 'مرتجع'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusOption(String status, String label) {
    final isSelected = _item.status == status;
    
    return ListTile(
      title: Text(label),
      leading: Radio<String>(
        value: status,
        groupValue: _item.status,
        onChanged: (value) {
          if (value != null) {
            Navigator.of(context).pop();
            _updateItemStatus(value);
          }
        },
      ),
      selected: isSelected,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${_item.brand} ${_item.model}'),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.canManageInventory && _item.status != 'sold') {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit_item':
                        _navigateToEditItem();
                        break;
                      case 'update_status':
                        _showStatusUpdateDialog();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit_item',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('تعديل الصنف'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'update_status',
                      child: ListTile(
                        leading: Icon(Icons.update),
                        title: Text('تحديث الحالة'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildImageSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildBasicInfoSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildPricingSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildLocationSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildStatusSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildMetadataSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'صورة بصمة الموتور',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: CachedNetworkImage(
                  imageUrl: _item.motorFingerprintImageUrl,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: double.infinity,
                    height: 200,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: double.infinity,
                    height: 200,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: Icon(Icons.broken_image, size: 48),
                    ),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'النص المستخرج:',
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _item.motorFingerprintText,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: 'monospace',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding * 2),

            // Chassis section
            Text(
              'صورة رقم الشاسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: CachedNetworkImage(
                  imageUrl: _item.chassisImageUrl,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: double.infinity,
                    height: 200,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: double.infinity,
                    height: 200,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: Icon(Icons.broken_image, size: 48),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'رقم الشاسية:',
                    style: Theme.of(context).textTheme.labelMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _item.chassisNumber,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: 'monospace',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            _buildInfoRow('النوع', _item.type),
            _buildInfoRow('الماركة', _item.brand),
            _buildInfoRow('الموديل', _item.model),
            _buildInfoRow('اللون', _item.color),
            _buildInfoRow('بلد المنشأ', _item.countryOfOrigin),
            _buildInfoRow('سنة الصنع', _item.yearOfManufacture.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    final profitAmount = _item.suggestedSellingPrice - _item.purchasePrice;
    final profitPercentage = (_item.purchasePrice > 0) 
        ? (profitAmount / _item.purchasePrice) * 100 
        : 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأسعار والأرباح',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            _buildInfoRow('سعر الشراء', AppUtils.formatCurrency(_item.purchasePrice)),
            _buildInfoRow('سعر البيع المقترح', AppUtils.formatCurrency(_item.suggestedSellingPrice)),
            _buildInfoRow('الربح المتوقع', AppUtils.formatCurrency(profitAmount)),
            _buildInfoRow('نسبة الربح', '${profitPercentage.toStringAsFixed(1)}%'),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الموقع',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Icon(
                  Icons.warehouse,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المخزن الحالي',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                      Text(
                        _warehouse?.name ?? 'جاري التحميل...',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_warehouse != null)
                        Text(
                          _warehouse!.address,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    switch (_item.status) {
      case 'available':
      case 'متاح':
        statusColor = Colors.green;
        statusText = 'متاح';
        statusIcon = Icons.check_circle;
        break;
      case 'sold':
      case 'مباع':
        statusColor = Colors.blue;
        statusText = 'مباع';
        statusIcon = Icons.shopping_cart;
        break;
      case 'transferred':
      case 'محول':
        statusColor = Colors.orange;
        statusText = 'محول';
        statusIcon = Icons.swap_horiz;
        break;
      case 'returned':
      case 'مرتجع':
        statusColor = Colors.red;
        statusText = 'مرتجع';
        statusIcon = Icons.keyboard_return;
        break;
      default:
        statusColor = Colors.grey;
        statusText = _item.status;
        statusIcon = Icons.help;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الحالة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                  child: Icon(
                    statusIcon,
                    color: statusColor,
                    size: 32,
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        statusText,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'آخر تحديث: ${AppUtils.formatDateTime(_item.updatedAt)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات إضافية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            _buildInfoRow('تاريخ الإضافة', AppUtils.formatDateTime(_item.createdAt)),
            _buildInfoRow('آخر تحديث', AppUtils.formatDateTime(_item.updatedAt)),
            _buildInfoRow('معرف الصنف', _item.id),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Navigate to edit item screen
  Future<void> _navigateToEditItem() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddItemScreen(
          editItem: _item,
        ),
      ),
    );

    // If item was updated, refresh the data
    if (result == true) {
      try {
        final updatedItem = await _dataService.getItemById(_item.id);
        if (updatedItem != null && mounted) {
          setState(() {
            _item = updatedItem;
          });
          AppUtils.showSnackBar(context, 'تم تحديث بيانات الصنف بنجاح');
        }
      } catch (e) {
        if (mounted) {
          AppUtils.showSnackBar(context, 'خطأ في تحديث البيانات: $e', isError: true);
        }
      }
    }
  }
}
