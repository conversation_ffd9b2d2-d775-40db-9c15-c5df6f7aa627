import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class ImageService {
  static ImageService? _instance;
  static ImageService get instance => _instance ??= ImageService._();

  ImageService._();

  Future<void> initialize() async {
    if (kDebugMode) {
      print('Image service initialized (desktop mode)');
    }
  }

  Future<bool> requestCameraPermission() async {
    // For desktop/web, always return true
    return true;
  }

  Future<Uint8List?> captureImage() async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Camera capture not supported in desktop mode');
    }
    return null;
  }

  Future<Uint8List?> pickImageFromGallery() async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Gallery picker not supported in desktop mode');
    }
    return null;
  }

  Future<String?> uploadImageToCloudinary(Uint8List imageBytes, String fileName) async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Image upload not supported in desktop mode');
    }
    return null;
  }

  Future<Uint8List?> createCompositeImage(List<Uint8List> images) async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Composite image creation not supported in desktop mode');
    }
    return null;
  }

  // Additional methods for desktop compatibility
  Future<Uint8List?> takeMotorFingerprintPhoto(BuildContext context) async {
    _showDesktopNotSupportedDialog(context, 'تصوير بصمة الموتور');
    return null;
  }

  Future<Uint8List?> takeChassisPhoto(BuildContext context) async {
    _showDesktopNotSupportedDialog(context, 'تصوير رقم الشاسيه');
    return null;
  }

  Future<Uint8List?> takeIdCardPhoto(BuildContext context, {required bool isFront}) async {
    _showDesktopNotSupportedDialog(context, 'تصوير البطاقة الشخصية');
    return null;
  }

  Future<bool> validateImageQuality(Uint8List image) async {
    // For desktop, always return true
    return true;
  }

  Future<Map<String, String>> extractMotorFingerprintFromImage(Uint8List image) async {
    // For desktop, return empty map
    return {};
  }

  Future<Map<String, String>> extractIdCardData(Uint8List frontImage, Uint8List backImage) async {
    // For desktop, return empty map
    return {};
  }

  Future<String?> uploadImage(Uint8List imageBytes, String fileName) async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Image upload not supported in desktop mode');
    }
    return null;
  }

  void _showDesktopNotSupportedDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('غير متاح في نسخة سطح المكتب'),
        content: Text('ميزة $feature غير متاحة في نسخة سطح المكتب.\nيرجى استخدام النسخة المحمولة للوصول لهذه الميزة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
