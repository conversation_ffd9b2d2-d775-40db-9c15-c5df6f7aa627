import 'package:flutter/foundation.dart';

class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();
  
  PermissionService._();

  Future<bool> requestCameraPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Camera permission granted (desktop mode)');
    }
    return true;
  }

  Future<bool> requestStoragePermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Storage permission granted (desktop mode)');
    }
    return true;
  }

  Future<bool> requestPhotosPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Photos permission granted (desktop mode)');
    }
    return true;
  }

  Future<Map<String, bool>> requestCameraAndStoragePermissions() async {
    // For desktop/web, always return true for all
    if (kDebugMode) {
      print('All permissions granted (desktop mode)');
    }
    return {
      'camera': true,
      'storage': true,
    };
  }

  Future<bool> hasCameraPermission() async {
    // For desktop/web, always return true
    return true;
  }

  String getPermissionStatusMessage(String status) {
    // Simple status message for desktop
    return 'مسموح';
  }

  Future<bool> requestNotificationPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Notification permission granted (desktop mode)');
    }
    return true;
  }

  Future<bool> requestLocationPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Location permission granted (desktop mode)');
    }
    return true;
  }

  Future<Map<String, bool>> getAllPermissionsStatus() async {
    // For desktop/web, all permissions are granted
    return {
      'camera': true,
      'storage': true,
      'photos': true,
      'notification': true,
      'location': true,
    };
  }
}
