import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import 'core/theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'services/local_database_service.dart';
import 'services/data_service.dart';

import 'screens/desktop/full_desktop_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🖥️ Starting El Farhan Desktop App (Fixed)...');
  }

  // Initialize services (without Firebase)
  await _initializeServices();

  runApp(const ElFarhanDesktopApp());
}

Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔄 Initializing services (offline mode)...');
    }

    // Initialize local database first
    await LocalDatabaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ Local database initialized');
    }

    // Skip Firebase initialization for desktop
    if (kDebugMode) {
      print('⚠️ Firebase skipped (desktop offline mode)');
    }

    // Data service is ready
    if (kDebugMode) {
      print('✅ Data service ready');
    }

    if (kDebugMode) {
      print('🎉 All services initialized successfully (offline mode)!');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing services: $e');
    }
  }
}

class ElFarhanDesktopApp extends StatelessWidget {
  const ElFarhanDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        Provider<DataService>(create: (_) => DataService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'الفرحان للنقل الخفيف - نسخة سطح المكتب',
            theme: AppTheme.lightTheme.copyWith(
              // Desktop-specific theme adjustments
              visualDensity: VisualDensity.adaptivePlatformDensity,
              // Better desktop colors
              colorScheme: AppTheme.lightTheme.colorScheme.copyWith(
                primary: Colors.blue.shade800,
                secondary: Colors.blue.shade600,
              ),
              // Fix text scaling for desktop
              textTheme: AppTheme.lightTheme.textTheme.apply(
                fontSizeFactor: 1.0,
              ),
            ),
            darkTheme: AppTheme.darkTheme,
            home: const DesktopWrapper(),
            debugShowCheckedModeBanner: false,
            // Desktop-specific configurations
            scrollBehavior: const MaterialScrollBehavior(),
          );
        },
      ),
    );
  }
}

class DesktopWrapper extends StatefulWidget {
  const DesktopWrapper({super.key});

  @override
  State<DesktopWrapper> createState() => _DesktopWrapperState();
}

class _DesktopWrapperState extends State<DesktopWrapper> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Add a small delay to ensure proper initialization
    await Future.delayed(const Duration(milliseconds: 500));
    
    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.blue.shade800,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.local_shipping,
                  color: Colors.white,
                  size: 50,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title
              const Text(
                'الفرحان للنقل الخفيف',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              
              const SizedBox(height: 8),
              
              const Text(
                'نسخة سطح المكتب',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Loading indicator
              const CircularProgressIndicator(),
              
              const SizedBox(height: 16),
              
              const Text(
                'جاري التحضير...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return const FullDesktopApp();
  }
}
