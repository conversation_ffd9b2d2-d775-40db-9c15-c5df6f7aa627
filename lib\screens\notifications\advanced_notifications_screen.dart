import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/notification_model.dart';
import '../../services/enhanced_notification_service.dart';
import '../../services/data_service.dart';

import '../sales/invoice_details_screen.dart';
import '../documents/document_tracking_screen.dart';
import '../reports/warehouse_movement_reports_screen.dart';
import '../inventory/inventory_screen.dart';
import '../sales/sales_screen.dart';
import '../admin/warehouse_management_screen.dart';
import '../admin/agents_management_screen.dart';

class AdvancedNotificationsScreen extends StatefulWidget {
  const AdvancedNotificationsScreen({super.key});

  @override
  State<AdvancedNotificationsScreen> createState() => _AdvancedNotificationsScreenState();
}

class _AdvancedNotificationsScreenState extends State<AdvancedNotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  final EnhancedNotificationService _notificationService = EnhancedNotificationService.instance;
  final DataService _dataService = DataService.instance;
  
  List<NotificationModel> _allNotifications = [];
  List<NotificationModel> _unreadNotifications = [];
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
    
    // Listen for new notifications
    _notificationService.onNotificationReceived = (notification) {
      if (mounted) {
        setState(() {
          _allNotifications.insert(0, notification);
          if (!notification.isRead) {
            _unreadNotifications.insert(0, notification);
          }
        });
      }
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get in-app notifications
      final inAppNotifications = _notificationService.inAppNotifications;
      
      // Get database notifications (if any)
      // This would typically load from your database
      
      setState(() {
        _allNotifications = List.from(inAppNotifications);
        _unreadNotifications = _allNotifications.where((n) => !n.isRead).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الإشعارات: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإشعارات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Unread count badge
          if (_notificationService.unreadCount > 0)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_notificationService.unreadCount}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          IconButton(
            icon: const Icon(Icons.mark_email_read),
            onPressed: _markAllAsRead,
            tooltip: 'تعليم الكل كمقروء',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showNotificationSettings,
            tooltip: 'إعدادات الإشعارات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'الكل (${_allNotifications.length})',
              icon: const Icon(Icons.notifications),
            ),
            Tab(
              text: 'غير مقروءة (${_unreadNotifications.length})',
              icon: const Icon(Icons.notifications_active),
            ),
            const Tab(
              text: 'الفلاتر',
              icon: Icon(Icons.filter_list),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل الإشعارات...'),
                ],
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationsList(_allNotifications),
                _buildNotificationsList(_unreadNotifications),
                _buildFiltersTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createTestNotification,
        backgroundColor: Theme.of(context).primaryColor,
        tooltip: 'إنشاء إشعار تجريبي',
        child: const Icon(Icons.add_alert, color: Colors.white),
      ),
    );
  }

  Widget _buildNotificationsList(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد إشعارات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.white : Colors.blue.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead ? Colors.grey.shade300 : Colors.blue.withAlpha(102),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getNotificationColor(notification.type).withAlpha(51),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: _getNotificationColor(notification.type),
            size: 24,
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey[500],
                ),
                const SizedBox(width: 4),
                Text(
                  _formatNotificationTime(notification.createdAt),
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type).withAlpha(51),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getNotificationTypeLabel(notification.type),
                    style: TextStyle(
                      color: _getNotificationColor(notification.type),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: notification.isRead
            ? null
            : Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
        onTap: () => _onNotificationTapped(notification),
        onLongPress: () => _showNotificationOptions(notification),
      ),
    );
  }

  Widget _buildFiltersTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلترة الإشعارات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Filter by type
          const Text(
            'نوع الإشعار:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip('all', 'الكل'),
              _buildFilterChip('transfer', 'تحويل المخزن'),
              _buildFilterChip('sale', 'المبيعات'),
              _buildFilterChip('payment', 'الدفعات'),
              _buildFilterChip('inventory_add', 'المخزون'),
              _buildFilterChip('document_update', 'الوثائق'),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Quick actions
          const Text(
            'إجراءات سريعة:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _markAllAsRead,
                  icon: const Icon(Icons.mark_email_read),
                  label: const Text('تعليم الكل كمقروء'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _clearAllNotifications,
                  icon: const Icon(Icons.clear_all),
                  label: const Text('مسح الكل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
        _applyFilter();
      },
      selectedColor: Theme.of(context).primaryColor.withAlpha(51),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  // Helper methods
  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'transfer': return Icons.swap_horiz;
      case 'sale': return Icons.shopping_cart;
      case 'payment': return Icons.payment;
      case 'inventory_add': return Icons.inventory;
      case 'document_update': return Icons.description;
      default: return Icons.notifications;
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'transfer': return Colors.blue;
      case 'sale': return Colors.green;
      case 'payment': return Colors.orange;
      case 'inventory_add': return Colors.purple;
      case 'document_update': return Colors.teal;
      default: return Colors.grey;
    }
  }

  String _getNotificationTypeLabel(String type) {
    switch (type) {
      case 'transfer': return 'تحويل';
      case 'sale': return 'مبيعات';
      case 'payment': return 'دفعة';
      case 'inventory_add': return 'مخزون';
      case 'document_update': return 'وثيقة';
      default: return 'عام';
    }
  }

  String _formatNotificationTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return DateFormat('dd/MM/yyyy').format(dateTime);
    }
  }

  void _onNotificationTapped(NotificationModel notification) {
    if (!notification.isRead) {
      _notificationService.markAsRead(notification.id);
      setState(() {
        _unreadNotifications.removeWhere((n) => n.id == notification.id);
      });
    }

    // Handle navigation based on notification data
    _handleNotificationNavigation(notification);
  }

  void _handleNotificationNavigation(NotificationModel notification) {
    final route = notification.data?['route'] as String?;
    final type = notification.type;
    final relatedId = notification.relatedId ??
                     notification.data?['invoiceId'] ??
                     notification.data?['transferId'] ??
                     notification.data?['documentId'];

    if (route != null) {
      _navigateToRoute(route, notification.data ?? {});
    } else if (type != null) {
      _navigateByType(type, relatedId, notification.data ?? {});
    } else {
      AppUtils.showSnackBar(context, 'لا يمكن التنقل لهذا الإشعار');
    }
  }

  void _navigateToRoute(String route, Map<String, dynamic> data) {
    try {
      if (route.startsWith('/invoice_details/')) {
        final invoiceId = route.split('/').last;
        _navigateToInvoiceDetails(invoiceId);
      } else if (route.startsWith('/document_tracking/')) {
        _navigateToDocumentTracking();
      } else if (route.startsWith('/transfer_details/')) {
        _navigateToTransferDetails();
      } else if (route.startsWith('/agent_statement/')) {
        final agentId = route.split('/').last;
        _navigateToAgentStatement(agentId);
      } else {
        _navigateToGeneralRoute(route);
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في التنقل: $e', isError: true);
    }
  }

  void _navigateByType(String type, String? relatedId, Map<String, dynamic> data) {
    try {
      switch (type) {
        case 'sale':
        case 'invoice':
          if (relatedId != null) {
            _navigateToInvoiceDetails(relatedId);
          } else {
            AppUtils.showSnackBar(context, 'معرف الفاتورة غير متوفر');
          }
          break;
        case 'document_status':
        case 'document_update':
          _navigateToDocumentTracking();
          break;
        case 'transfer':
          _navigateToTransferDetails();
          break;
        case 'payment':
          final agentId = data['agentId'] as String?;
          if (agentId != null) {
            _navigateToAgentStatement(agentId);
          } else {
            AppUtils.showSnackBar(context, 'معرف الوكيل غير متوفر');
          }
          break;
        default:
          AppUtils.showSnackBar(context, 'نوع إشعار غير معروف: $type');
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في التنقل: $e', isError: true);
    }
  }

  void _showNotificationOptions(NotificationModel notification) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.mark_email_read),
              title: const Text('تعليم كمقروء'),
              onTap: () {
                Navigator.pop(context);
                if (!notification.isRead) {
                  _notificationService.markAsRead(notification.id);
                  setState(() {
                    _unreadNotifications.removeWhere((n) => n.id == notification.id);
                  });
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('حذف الإشعار'),
              onTap: () {
                Navigator.pop(context);
                _deleteNotification(notification);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _deleteNotification(NotificationModel notification) {
    setState(() {
      _allNotifications.removeWhere((n) => n.id == notification.id);
      _unreadNotifications.removeWhere((n) => n.id == notification.id);
    });
    AppUtils.showSnackBar(context, 'تم حذف الإشعار');
  }

  void _markAllAsRead() {
    _notificationService.markAllAsRead();
    setState(() {
      _unreadNotifications.clear();
    });
    AppUtils.showSnackBar(context, 'تم تعليم جميع الإشعارات كمقروءة');
  }

  void _clearAllNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد المسح'),
        content: const Text('هل تريد مسح جميع الإشعارات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _notificationService.clearNotifications();
              setState(() {
                _allNotifications.clear();
                _unreadNotifications.clear();
              });
              AppUtils.showSnackBar(context, 'تم مسح جميع الإشعارات');
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _applyFilter() {
    _loadNotifications();
  }

  void _showNotificationSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificationSettingsScreen(),
      ),
    );
  }

  void _createTestNotification() {
    _notificationService.createNotification(
      title: 'إشعار تجريبي',
      body: 'هذا إشعار تجريبي لاختبار النظام',
      type: 'general',
      data: {
        'route': '/test',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    AppUtils.showSnackBar(context, 'تم إنشاء إشعار تجريبي');
  }

  // Navigation helper methods
  void _navigateToInvoiceDetails(String invoiceId) async {
    try {
      final invoice = await _dataService.getInvoiceById(invoiceId);
      if (invoice != null && mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => InvoiceDetailsScreen(invoice: invoice),
          ),
        );
      } else if (mounted) {
        AppUtils.showSnackBar(context, 'لم يتم العثور على الفاتورة', isError: true);
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الفاتورة: $e', isError: true);
      }
    }
  }

  void _navigateToDocumentTracking() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DocumentTrackingScreen(),
      ),
    );
  }

  void _navigateToTransferDetails() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const WarehouseMovementReportsScreen(),
      ),
    );
  }

  void _navigateToAgentStatement(String agentId) async {
    try {
      final agent = await _dataService.getUserById(agentId);
      if (agent != null && mounted) {
        // For now, show a message since AgentStatementScreen might not exist
        AppUtils.showSnackBar(context, 'تم العثور على بيانات الوكيل: ${agent.fullName}');
      } else if (mounted) {
        AppUtils.showSnackBar(context, 'لم يتم العثور على بيانات الوكيل', isError: true);
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل بيانات الوكيل: $e', isError: true);
      }
    }
  }

  void _navigateToGeneralRoute(String route) {
    switch (route) {
      case '/inventory':
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const InventoryScreen(),
          ),
        );
        break;
      case '/sales':
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const SalesScreen(),
          ),
        );
        break;
      case '/warehouse_management':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const WarehouseManagementScreen(),
          ),
        );
        break;
      case '/agent_management':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const AgentsManagementScreen(),
          ),
        );
        break;
      case '/reports':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const WarehouseMovementReportsScreen(),
          ),
        );
        break;
      default:
        AppUtils.showSnackBar(context, 'الشاشة $route قيد التطوير');
    }
  }
}

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final EnhancedNotificationService _notificationService = EnhancedNotificationService.instance;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const Text(
            'الإعدادات العامة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          SwitchListTile(
            title: const Text('تفعيل الإشعارات'),
            subtitle: const Text('تشغيل/إيقاف جميع الإشعارات'),
            value: _notificationService.notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationService.updateNotificationSettings(enabled: value);
              });
            },
          ),

          SwitchListTile(
            title: const Text('الأصوات'),
            subtitle: const Text('تشغيل أصوات الإشعارات'),
            value: _notificationService.soundEnabled,
            onChanged: (value) {
              setState(() {
                _notificationService.updateNotificationSettings(sound: value);
              });
            },
          ),

          SwitchListTile(
            title: const Text('الاهتزاز'),
            subtitle: const Text('تفعيل الاهتزاز مع الإشعارات'),
            value: _notificationService.vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _notificationService.updateNotificationSettings(vibration: value);
              });
            },
          ),
        ],
      ),
    );
  }


}
