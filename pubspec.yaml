name: el_<PERSON>han_app
description: "تطبيق آل فرحان للنقل الخفيف"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State Management
  provider: ^6.1.2

  # UI Components
  cupertino_icons: ^1.0.8

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.4
  firebase_messaging: ^15.1.3
  firebase_storage: ^12.3.4

  # Local Database
  sqflite: ^2.4.2
  sqflite_common_ffi: ^2.3.3  # For desktop support
  sqlite3_flutter_libs: ^0.5.24  # For desktop SQLite
  path: ^1.9.1

  # Network & Connectivity
  connectivity_plus: ^6.1.0
  dio: ^5.8.0+1
  http: ^1.1.0

  # Image Handling
  image_picker: ^1.1.2
  image: ^4.3.0
  cached_network_image: ^3.4.1

  # OCR & ML
  google_ml_kit: ^0.18.1

  # Cloud Storage
  cloudinary_public: ^0.23.1

  # Permissions (mobile only)
  # permission_handler: ^11.4.0  # Disabled for desktop

  # Notifications
  flutter_local_notifications: ^18.0.1

  # Local Storage
  shared_preferences: ^2.5.3

  # Date & Time
  intl: ^0.19.0

  # PDF & Printing
  pdf: ^3.11.3
  printing: ^5.14.2

  # Utilities
  uuid: ^4.5.1
  shimmer: ^3.0.0
  url_launcher: ^6.3.1
  google_fonts: ^6.2.1
  camera: ^0.10.6
  google_mlkit_text_recognition: ^0.13.1

  # Security and encryption
  crypto: ^3.0.3
  encrypt: ^5.0.3

  # File operations
  path_provider: ^2.1.4
  share_plus: ^10.0.2

  # Desktop specific (simplified)
  window_manager: ^0.4.2  # Window management for desktop

  # Charts and graphs
  fl_chart: ^0.69.0

  # Push notifications

  # Deep linking

  # Connectivity monitoring

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - assets/images/
    - assets/icons/

  # Arabic Fonts
  fonts:
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
