import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/user_model.dart';
import '../../models/agent_account_model.dart';
import '../../models/invoice_model.dart';
import '../../models/payment_model.dart';
import '../../widgets/transfer_details_dialog.dart';
import '../../services/data_service.dart';
import '../../services/enhanced_pdf_service.dart';

class DetailedAgentStatementScreen extends StatefulWidget {
  final UserModel agent;

  const DetailedAgentStatementScreen({
    super.key,
    required this.agent,
  });

  @override
  State<DetailedAgentStatementScreen> createState() => _DetailedAgentStatementScreenState();
}

class _DetailedAgentStatementScreenState extends State<DetailedAgentStatementScreen>
    with SingleTickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  
  late TabController _tabController;
  
  AgentAccountModel? _agentAccount;
  List<InvoiceModel> _agentInvoices = [];
  List<PaymentModel> _agentPayments = [];
  final List<Map<String, dynamic>> _allTransactions = [];
  
  bool _isLoading = false;
  
  // Filters
  DateTimeRange? _selectedDateRange;
  String _selectedFilter = 'all'; // all, sales, payments, profits
  
  // Statistics
  double _totalSales = 0;
  double _totalProfits = 0;
  double _totalPayments = 0;
  double _currentBalance = 0;
  double _profitPercentage = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAgentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAgentData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load agent account
      final accounts = await _dataService.getAllAgentAccounts();
      _agentAccount = accounts.firstWhere(
        (account) => account.agentId == widget.agent.id,
        orElse: () => AgentAccountModel(
          id: AppUtils.generateId(),
          agentId: widget.agent.id,
          agentName: widget.agent.fullName,
          agentPhone: widget.agent.phone,
          totalDebt: 0,
          totalPaid: 0,
          currentBalance: 0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'system',
        ),
      );

      // Load agent invoices
      _agentInvoices = await _dataService.getAgentInvoices(widget.agent.id);

      // Load agent payments
      final paymentsData = await _dataService.getAgentPayments(widget.agent.id);
      _agentPayments = paymentsData.map((data) {
        try {
          return PaymentModel.fromMap(data);
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error parsing payment data: $data');
            print('❌ Error: $e');
          }
          // Return a default payment model if parsing fails
          return PaymentModel(
            id: data['id']?.toString() ?? AppUtils.generateId(),
            agentId: data['agentId']?.toString() ?? widget.agent.id,
            amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
            paymentMethod: data['paymentMethod']?.toString() ?? 'cash',
            paymentDate: DateTime.tryParse(data['createdAt']?.toString() ?? '') ?? DateTime.now(),
            notes: data['notes']?.toString() ?? '',
            receiptNumber: data['id']?.toString() ?? '',
            createdBy: data['createdBy']?.toString() ?? 'system',
            createdAt: DateTime.tryParse(data['createdAt']?.toString() ?? '') ?? DateTime.now(),
            updatedAt: DateTime.tryParse(data['updatedAt']?.toString() ?? '') ?? DateTime.now(),
          );
        }
      }).toList();

      // Calculate statistics
      _calculateStatistics();

      // Prepare all transactions for display
      _prepareAllTransactions();

      if (kDebugMode) {
        print('✅ Loaded agent data for ${widget.agent.fullName}:');
        print('   Invoices: ${_agentInvoices.length}');
        print('   Payments: ${_agentPayments.length}');
        print('   Total Sales: $_totalSales');
        print('   Total Profits: $_totalProfits');
        print('   Current Balance: $_currentBalance');
        print('   All Transactions: ${_allTransactions.length}');
        for (var transaction in _allTransactions.take(3)) {
          print('   Transaction: ${transaction['type']} - ${transaction['description']} - Debit: ${transaction['debit']} - Credit: ${transaction['credit']}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent data: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateStatistics() {
    // Calculate sales only from customer invoices (actual sales)
    _totalSales = _agentInvoices
        .where((invoice) => invoice.type == 'customer')
        .fold(0, (sum, invoice) => sum + invoice.sellingPrice);

    _totalProfits = _agentInvoices.fold(0, (sum, invoice) => sum + invoice.agentProfitShare);
    _totalPayments = _agentPayments.fold(0, (sum, payment) => sum + payment.amount);

    // Calculate total debt correctly:
    // 1. Transfer invoices: full amount owed (cost of goods transferred)
    // 2. Customer sales: only company's profit share (50% of profit)
    double totalDebt = 0;
    double transferDebt = 0;
    double profitShareDebt = 0;

    for (final invoice in _agentInvoices) {
      if (invoice.type == 'agent' ||
          invoice.type == 'goods' ||
          (invoice.customerData != null &&
           invoice.customerData!['transferType'] == 'warehouse_transfer') ||
          (invoice.additionalData != null &&
           invoice.additionalData!['transferType'] == 'goods_transfer')) {
        // This is a transfer invoice - agent owes the full transfer amount
        transferDebt += invoice.sellingPrice; // or invoice.purchasePrice
        totalDebt += invoice.sellingPrice;

        if (kDebugMode) {
          print('📦 Transfer debt: ${invoice.invoiceNumber} (${invoice.type}) = ${AppUtils.formatCurrency(invoice.sellingPrice)}');
          print('   additionalData: ${invoice.additionalData}');
          print('   customerData: ${invoice.customerData}');
        }
      } else if (invoice.type == 'customer') {
        // This is a customer sale - agent owes only company's profit share
        profitShareDebt += invoice.companyProfitShare;
        totalDebt += invoice.companyProfitShare;

        if (kDebugMode) {
          print('💰 Profit share debt: ${invoice.invoiceNumber} (${invoice.type}) = ${AppUtils.formatCurrency(invoice.companyProfitShare)}');
          print('   sellingPrice: ${invoice.sellingPrice}, purchasePrice: ${invoice.purchasePrice}');
        }
      }
    }

    // Current balance = Total Debt - Total Payments
    _currentBalance = totalDebt - _totalPayments;

    // Calculate profit percentage based on agent's actual profit share
    if (_totalSales > 0) {
      _profitPercentage = (_totalProfits / _totalSales) * 100;
    } else {
      _profitPercentage = 0;
    }

    if (kDebugMode) {
      print('📊 Agent Statement Calculations (CORRECTED):');
      print('   Customer Sales Only: ${AppUtils.formatCurrency(_totalSales)}');
      print('   Transfer Debt: ${AppUtils.formatCurrency(transferDebt)}');
      print('   Profit Share Debt: ${AppUtils.formatCurrency(profitShareDebt)}');
      print('   Total Debt: ${AppUtils.formatCurrency(totalDebt)}');
      print('   Total Agent Profits: ${AppUtils.formatCurrency(_totalProfits)}');
      print('   Total Payments: ${AppUtils.formatCurrency(_totalPayments)}');
      print('   Current Balance: ${AppUtils.formatCurrency(_currentBalance)}');
    }
  }

  void _prepareAllTransactions() {
    _allTransactions.clear();

    // Add transactions based on invoice type
    for (final invoice in _agentInvoices) {
      if (invoice.type == 'agent' ||
          invoice.type == 'goods' ||
          (invoice.customerData != null &&
           invoice.customerData!['transferType'] == 'warehouse_transfer') ||
          (invoice.additionalData != null &&
           invoice.additionalData!['transferType'] == 'goods_transfer')) {
        // This is a TRANSFER invoice - agent owes the full transfer amount
        _allTransactions.add({
          'type': 'transfer',
          'date': invoice.createdAt,
          'description': 'تحويل بضاعة - ${invoice.invoiceNumber}\n'
                        'تكلفة البضاعة المحولة من المؤسسة للوكيل',
          'debit': invoice.sellingPrice, // Full transfer amount
          'credit': 0.0,
          'balance_change': invoice.sellingPrice,
          'reference': invoice.invoiceNumber.length > 8 ? invoice.invoiceNumber.substring(0, 8) : invoice.invoiceNumber,
          'full_reference': invoice.invoiceNumber,
          'data': invoice,
        });
      } else if (invoice.type == 'customer') {
        // This is a CUSTOMER SALE - show both revenue and debt
        final totalProfit = invoice.sellingPrice - invoice.purchasePrice;
        final companyShare = invoice.companyProfitShare;
        final agentShare = invoice.agentProfitShare;
        final agentPercentage = widget.agent.profitSharePercentage * 100;

        // 1. Add sale revenue (credit to agent)
        _allTransactions.add({
          'type': 'sale_revenue',
          'date': invoice.createdAt,
          'description': 'إيراد بيع للعميل - ${invoice.customerName ?? 'عميل'}\n'
                        'سعر البيع: ${AppUtils.formatCurrency(invoice.sellingPrice)} | '
                        'تكلفة: ${AppUtils.formatCurrency(invoice.purchasePrice)} | '
                        'ربح إجمالي: ${AppUtils.formatCurrency(totalProfit)}',
          'debit': 0.0,
          'credit': invoice.sellingPrice, // Full sale amount as credit
          'balance_change': -invoice.sellingPrice, // Reduces debt
          'reference': invoice.invoiceNumber.length > 8 ? invoice.invoiceNumber.substring(0, 8) : invoice.invoiceNumber,
          'full_reference': invoice.invoiceNumber,
          'data': invoice,
        });

        // 2. Add company profit share (debit to agent)
        if (companyShare > 0) {
          _allTransactions.add({
            'type': 'sale_profit',
            'date': invoice.createdAt,
            'description': 'نصيب المؤسسة من ربح البيع - ${invoice.customerName ?? 'عميل'}\n'
                          'نصيب المؤسسة (${(100 - agentPercentage).toStringAsFixed(0)}%): ${AppUtils.formatCurrency(companyShare)} | '
                          'نصيب الوكيل (${agentPercentage.toStringAsFixed(0)}%): ${AppUtils.formatCurrency(agentShare)}',
            'debit': companyShare, // Company's profit share as debt
            'credit': 0.0,
            'balance_change': companyShare,
            'reference': invoice.invoiceNumber.length > 8 ? invoice.invoiceNumber.substring(0, 8) : invoice.invoiceNumber,
            'full_reference': invoice.invoiceNumber,
            'data': invoice,
            'totalProfit': totalProfit,
            'agentShare': agentShare,
            'companyShare': companyShare,
          });
        }
      }
    }

    // Add payment transactions (money paid by agent to company) - CREDIT
    for (final payment in _agentPayments) {
      _allTransactions.add({
        'type': 'payment',
        'date': payment.paymentDate,
        'description': 'دفعة نقدية من الوكيل',
        'debit': 0.0,
        'credit': payment.amount,
        'balance_change': payment.amount,
        'reference': payment.receiptNumber.length > 8 ? payment.receiptNumber.substring(0, 8) : payment.receiptNumber,
        'full_reference': payment.receiptNumber,
        'data': payment,
      });
    }

    // Sort by date (oldest first)
    _allTransactions.sort((a, b) =>
      (a['date'] as DateTime).compareTo(b['date'] as DateTime)
    );
  }



  List<Map<String, dynamic>> get _filteredTransactions {
    var filtered = _allTransactions.where((transaction) {
      // Filter by type
      if (_selectedFilter != 'all') {
        if (_selectedFilter == 'sales' && transaction['type'] != 'sale') return false;
        if (_selectedFilter == 'payments' && transaction['type'] != 'payment') return false;
        if (_selectedFilter == 'profits' && transaction['profit'] <= 0) return false;
      }

      // Filter by date range
      if (_selectedDateRange != null) {
        final transactionDate = transaction['date'] as DateTime;
        if (transactionDate.isBefore(_selectedDateRange!.start) ||
            transactionDate.isAfter(_selectedDateRange!.end.add(const Duration(days: 1)))) {
          return false;
        }
      }

      return true;
    }).toList();

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كشف حساب ${widget.agent.fullName}'),
        actions: [
          // Enhanced PDF Export Button
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _exportToPDF,
              icon: const Icon(Icons.picture_as_pdf, size: 18),
              label: const Text('PDF', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAgentData,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'نظرة عامة'),
            Tab(text: 'كشف الحساب'),
            Tab(text: 'الإحصائيات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildStatementTab(),
                _buildStatisticsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Agent Info Card
          _buildAgentInfoCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Summary Cards
          _buildSummaryCards(),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Recent Activity
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildStatementTab() {
    return Column(
      children: [
        // Filters
        _buildFilters(),
        
        // Transactions Table
        Expanded(
          child: _buildTransactionsTable(),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Charts
          _buildPerformanceCharts(),
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Detailed Statistics
          _buildDetailedStatistics(),
        ],
      ),
    );
  }

  Widget _buildAgentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    widget.agent.fullName.isNotEmpty 
                        ? widget.agent.fullName[0].toUpperCase()
                        : 'و',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.agent.fullName,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الهاتف: ${widget.agent.phone}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      Text(
                        'معرف الوكيل: ${widget.agent.id}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي المبيعات',
            _totalSales,
            Colors.blue,
            Icons.trending_up,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الأرباح',
            _totalProfits,
            Colors.green,
            Icons.monetization_on,
          ),
        ),
      ],
    );
  }



  Widget _buildRecentActivity() {
    final recentTransactions = _allTransactions.take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'آخر النشاطات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (recentTransactions.isEmpty)
              const Center(
                child: Text('لا توجد معاملات حديثة'),
              )
            else
              ...recentTransactions.map((transaction) => _buildActivityItem(transaction)),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> transaction) {
    final type = transaction['type'] as String;
    final debit = (transaction['debit'] as num?)?.toDouble() ?? 0.0;
    final credit = (transaction['credit'] as num?)?.toDouble() ?? 0.0;
    final amount = debit > 0 ? debit : credit;
    final isDebit = debit > 0;
    final date = transaction['date'] as DateTime;
    final description = transaction['description'] as String;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: isDebit ? Colors.red.withAlpha(51) : Colors.green.withAlpha(51),
            child: Icon(
              type == 'payment' ? Icons.payment :
              type == 'transfer' ? Icons.arrow_downward : Icons.trending_up,
              size: 16,
              color: isDebit ? Colors.red : Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  AppUtils.formatDate(date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${isDebit ? '-' : '+'}${AppUtils.formatCurrency(amount)}',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isDebit ? Colors.red : Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة البيانات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: const InputDecoration(
                      labelText: 'نوع المعاملة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع المعاملات')),
                      DropdownMenuItem(value: 'sales', child: Text('المبيعات فقط')),
                      DropdownMenuItem(value: 'payments', child: Text('الدفعات فقط')),
                      DropdownMenuItem(value: 'profits', child: Text('الأرباح فقط')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFilter = value ?? 'all';
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _selectDateRange,
                    icon: const Icon(Icons.date_range, size: 16),
                    label: Text(
                      _selectedDateRange == null
                          ? 'فترة'
                          : '${AppUtils.formatDate(_selectedDateRange!.start).substring(5, 10)} - ${AppUtils.formatDate(_selectedDateRange!.end).substring(5, 10)}',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(fontSize: 10),
                    ),
                  ),
                ),
              ],
            ),
            if (_selectedDateRange != null || _selectedFilter != 'all') ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    'عدد المعاملات: ${_filteredTransactions.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedDateRange = null;
      _selectedFilter = 'all';
    });
  }

  Widget _buildTransactionsTable() {
    final transactions = _filteredTransactions;

    if (transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد معاملات تطابق الفلاتر المحددة',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Column(
        children: [
          // Enhanced Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(26),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(Icons.account_balance_wallet,
                     color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'كشف حساب تفصيلي - ${transactions.length} معاملة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Improved Table
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
                ),
                child: DataTable(
                  columnSpacing: 8,
                  horizontalMargin: 12,
                  headingRowHeight: 50,
                  dataRowMinHeight: 60,
                  dataRowMaxHeight: 80,
                  headingRowColor: WidgetStateProperty.all(Colors.grey[50]),
                  columns: const [
                    DataColumn(
                      label: Text('التاريخ',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                    DataColumn(
                      label: Text('النوع',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                    DataColumn(
                      label: Text('الوصف',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                    DataColumn(
                      label: Text('مدين',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                    DataColumn(
                      label: Text('دائن',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                    DataColumn(
                      label: Text('المرجع',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 11)),
                    ),
                  ],
                  rows: transactions.map((transaction) => _buildEnhancedTransactionRow(transaction)).toList(),
                ),
              ),
            ),
          ),

          // Summary Section
          const SizedBox(height: 16),
          _buildSummarySection(transactions),
        ],
      ),
    );
  }

  DataRow _buildEnhancedTransactionRow(Map<String, dynamic> transaction) {
    final type = transaction['type'] as String;
    final date = transaction['date'] as DateTime;
    final description = transaction['description'] as String;
    final debit = (transaction['debit'] as num?)?.toDouble() ?? 0.0;
    final credit = (transaction['credit'] as num?)?.toDouble() ?? 0.0;
    final reference = transaction['reference'] as String? ?? '-';

    // Determine colors and labels based on transaction type
    Color typeColor;
    String typeLabel;
    IconData typeIcon;
    switch (type) {
      case 'payment':
        typeColor = Colors.green;
        typeLabel = 'دفعة';
        typeIcon = Icons.payment;
        break;
      case 'transfer':
        typeColor = Colors.blue;
        typeLabel = 'تحويل';
        typeIcon = Icons.swap_horiz;
        break;
      case 'sale_profit':
        typeColor = Colors.orange;
        typeLabel = 'ربح بيع';
        typeIcon = Icons.trending_up;
        break;
      default:
        typeColor = Colors.grey;
        typeLabel = 'أخرى';
        typeIcon = Icons.help_outline;
        break;
    }

    // Shorten reference to 8 characters
    final shortReference = reference.length > 8 ?
        '${reference.substring(0, 8)}...' : reference;

    return DataRow(
      onSelectChanged: type == 'transfer' ? (selected) {
        if (selected == true) {
          _showTransferDetails(transaction);
        }
      } : null,
      color: type == 'transfer' ?
        WidgetStateProperty.all(Colors.blue.withAlpha(13)) : null,
      cells: [
        DataCell(
          SizedBox(
            width: 80,
            child: Text(
              AppUtils.formatDate(date),
              style: const TextStyle(fontSize: 11),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: typeColor.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: typeColor.withAlpha(102)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(typeIcon, size: 12, color: typeColor),
                const SizedBox(width: 4),
                Text(
                  typeLabel,
                  style: TextStyle(
                    color: typeColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
        DataCell(
          SizedBox(
            width: 150,
            child: Text(
              description,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 11),
            ),
          ),
        ),
        // مدين (Debit)
        DataCell(
          SizedBox(
            width: 80,
            child: Text(
              debit > 0 ? AppUtils.formatCurrency(debit) : '-',
              style: TextStyle(
                color: debit > 0 ? Colors.red : Colors.grey,
                fontWeight: FontWeight.bold,
                fontSize: 11,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // دائن (Credit)
        DataCell(
          SizedBox(
            width: 80,
            child: Text(
              credit > 0 ? AppUtils.formatCurrency(credit) : '-',
              style: TextStyle(
                color: credit > 0 ? Colors.green : Colors.grey,
                fontWeight: FontWeight.bold,
                fontSize: 11,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        // المرجع
        DataCell(
          SizedBox(
            width: 70,
            child: Tooltip(
              message: reference,
              child: Text(
                shortReference,
                style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSummarySection(List<Map<String, dynamic>> transactions) {
    double totalDebit = 0.0;
    double totalCredit = 0.0;

    for (final transaction in transactions) {
      totalDebit += (transaction['debit'] as num?)?.toDouble() ?? 0.0;
      totalCredit += (transaction['credit'] as num?)?.toDouble() ?? 0.0;
    }

    final netBalance = totalCredit - totalDebit;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجمالي الحساب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'إجمالي المدين',
                      totalDebit,
                      Colors.red,
                      Icons.arrow_upward,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSummaryCard(
                    'إجمالي الدائن',
                    totalCredit,
                    Colors.green,
                    Icons.arrow_downward,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryCard(
                    'الرصيد الصافي',
                    netBalance.abs(),
                    netBalance >= 0 ? Colors.green : Colors.red,
                    netBalance >= 0 ? Icons.trending_up : Icons.trending_down,
                    subtitle: netBalance >= 0 ? 'لصالح المؤسسة' : 'لصالح الوكيل',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, double value, Color color, IconData icon, {String? subtitle}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(51)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            AppUtils.formatCurrency(value),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: color.withAlpha(179),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPerformanceCharts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مؤشرات الأداء',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceIndicator(
                    'نسبة الربح',
                    '${_profitPercentage.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildPerformanceIndicator(
                    'الرصيد الحالي',
                    AppUtils.formatCurrency(_currentBalance),
                    Icons.account_balance_wallet,
                    _currentBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceIndicator(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(76)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات تفصيلية',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatisticRow('عدد المبيعات للعملاء', '${_agentInvoices.where((inv) => inv.type == 'customer').length} فاتورة'),
            _buildStatisticRow('عدد التحويلات', '${_agentInvoices.where((inv) => inv.type != 'customer').length} فاتورة'),
            _buildStatisticRow('عدد الدفعات', '${_agentPayments.length} دفعة'),
            _buildStatisticRow('متوسط قيمة البيع للعملاء',
              _agentInvoices.where((inv) => inv.type == 'customer').isNotEmpty
                ? AppUtils.formatCurrency(_agentInvoices.where((inv) => inv.type == 'customer').fold<double>(0, (sum, inv) => sum + inv.sellingPrice) / _agentInvoices.where((inv) => inv.type == 'customer').length)
                : '0 ج.م'
            ),
            _buildStatisticRow('متوسط الربح للبيع',
              _agentInvoices.isNotEmpty
                ? AppUtils.formatCurrency(_totalProfits / _agentInvoices.length)
                : '0 ج.م'
            ),
            _buildStatisticRow('متوسط قيمة الدفعة',
              _agentPayments.isNotEmpty
                ? AppUtils.formatCurrency(_totalPayments / _agentPayments.length)
                : '0 ج.م'
            ),
            const Divider(),
            _buildStatisticRow('إجمالي المبيعات', AppUtils.formatCurrency(_totalSales), isTotal: true),
            _buildStatisticRow('إجمالي أرباح الوكيل', AppUtils.formatCurrency(_totalProfits), isTotal: true),
            _buildStatisticRow('إجمالي المدفوع', AppUtils.formatCurrency(_totalPayments), isTotal: true),
            const Divider(),
            // Profit sharing breakdown
            _buildProfitSharingSection(),
            _buildStatisticRow('الرصيد الحالي', AppUtils.formatCurrency(_currentBalance),
              isTotal: true,
              color: _currentBalance >= 0 ? Colors.green : Colors.red
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticRow(String label, String value, {bool isTotal = false, Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: color ?? (isTotal ? Theme.of(context).primaryColor : null),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitSharingSection() {
    // Calculate totals from this agent's sales
    double totalPurchasePrices = 0;
    double totalCompanyProfits = 0;
    double totalAgentProfits = 0;
    double totalSalesProfit = 0;
    double totalDebt = 0;

    for (final invoice in _agentInvoices) {
      totalPurchasePrices += invoice.purchasePrice;
      totalCompanyProfits += invoice.companyProfitShare;
      totalAgentProfits += invoice.agentProfitShare;
      totalSalesProfit += (invoice.sellingPrice - invoice.purchasePrice);
      totalDebt += invoice.purchasePrice + invoice.companyProfitShare;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'نظام المديونية وتقسيم الأرباح',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'المديونية تتكون من:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(height: 4),
          _buildStatisticRow('1. أسعار الشراء الأصلية', AppUtils.formatCurrency(totalPurchasePrices)),
          _buildStatisticRow('2. نصيب المؤسسة من الأرباح (50%)', AppUtils.formatCurrency(totalCompanyProfits)),
          const Divider(),
          _buildStatisticRow('إجمالي المديونية', AppUtils.formatCurrency(totalDebt), isTotal: true),
          const SizedBox(height: 8),
          Text(
            'تقسيم الأرباح (50% - 50%):',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 4),
          _buildStatisticRow('إجمالي أرباح المبيعات', AppUtils.formatCurrency(totalSalesProfit)),
          _buildStatisticRow('نصيب المؤسسة (${((1.0 - widget.agent.profitSharePercentage) * 100).toStringAsFixed(0)}%)', AppUtils.formatCurrency(totalCompanyProfits)),
          _buildStatisticRow('نصيب الوكيل (${(widget.agent.profitSharePercentage * 100).toStringAsFixed(0)}%)', AppUtils.formatCurrency(totalAgentProfits)),
          const Divider(),
          _buildStatisticRow(
            'نسبة التقسيم المحددة',
            '${(widget.agent.profitSharePercentage * 100).toStringAsFixed(1)}% للوكيل',
            color: Colors.green[700],
          ),
        ],
      ),
    );
  }

  Future<void> _exportToPDF() async {
    try {
      // Show loading
      if (mounted) {
        AppUtils.showSnackBar(context, 'جاري إنشاء ملف PDF...');
      }

      // Prepare statistics
      final statistics = {
        'totalSales': _totalSales,
        'totalProfits': _totalProfits,
        'totalPaid': _totalPayments,
        'currentBalance': _currentBalance,
        'profitPercentage': _profitPercentage,
        'transactionCount': _allTransactions.length,
      };

      // Generate PDF using enhanced service with corrected transactions
      final pdfBytes = await EnhancedPdfService.instance.generateAgentStatementPDFWithTransactions(
        agent: widget.agent,
        account: _agentAccount!,
        transactions: _allTransactions, // استخدام المعاملات المصححة
        statistics: statistics,
      );

      // Share PDF
      await EnhancedPdfService.instance.saveAndSharePDF(
        pdfBytes,
        'كشف_حساب_${widget.agent.fullName}_${AppUtils.formatDate(DateTime.now())}.pdf',
      );

      if (mounted) {
        AppUtils.showSnackBar(context, '✅ تم تصدير كشف الحساب بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting PDF: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
      }
    }
  }

  void _showTransferDetails(Map<String, dynamic> transaction) {
    showDialog(
      context: context,
      builder: (context) => TransferDetailsDialog(
        transaction: transaction,
        agentName: widget.agent.fullName,
      ),
    );
  }
}
