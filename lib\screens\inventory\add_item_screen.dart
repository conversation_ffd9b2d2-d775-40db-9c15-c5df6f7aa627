import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/image_service.dart';
import '../../services/data_service.dart';
import '../../services/permission_service.dart';
import '../../services/ocr_service.dart';
import '../../providers/auth_provider.dart';

class AddItemScreen extends StatefulWidget {
  final ItemModel? editItem;

  const AddItemScreen({
    super.key,
    this.editItem,
  });

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _typeController = TextEditingController();
  final _modelController = TextEditingController();
  final _colorController = TextEditingController();
  final _brandController = TextEditingController();
  final _countryController = TextEditingController();
  final _yearController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _motorFingerprintController = TextEditingController();
  final _chassisNumberController = TextEditingController();

  final ImageService _imageService = ImageService.instance;
  final DataService _dataService = DataService.instance;
  final PermissionService _permissionService = PermissionService.instance;

  dynamic _motorFingerprintImage; // Can be File or Uint8List
  dynamic _chassisImage; // Can be File or Uint8List
  String? _selectedWarehouseId;
  List<WarehouseModel> _warehouses = [];
  bool _isLoading = false;
  bool _isProcessingImage = false;
  bool _isProcessingChassisImage = false;

  final List<String> _vehicleTypes = [
    'موتوسيكل',
    'تروسيكل',
    'سكوتر كهرباء',
    'توكتوك',
  ];

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
    _initializeForEdit();
  }

  /// Initialize form for editing if editItem is provided
  void _initializeForEdit() {
    if (widget.editItem != null) {
      final item = widget.editItem!;
      _typeController.text = item.type;
      _modelController.text = item.model;
      _colorController.text = item.color;
      _brandController.text = item.brand;
      _countryController.text = item.countryOfOrigin;
      _yearController.text = item.yearOfManufacture.toString();
      _purchasePriceController.text = item.purchasePrice.toString();
      _sellingPriceController.text = item.suggestedSellingPrice.toString();
      _motorFingerprintController.text = item.motorFingerprintText;
      _chassisNumberController.text = item.chassisNumber;
      _selectedWarehouseId = item.currentWarehouseId;
    }
  }

  @override
  void dispose() {
    _typeController.dispose();
    _modelController.dispose();
    _colorController.dispose();
    _brandController.dispose();
    _countryController.dispose();
    _yearController.dispose();
    _purchasePriceController.dispose();
    _sellingPriceController.dispose();
    _motorFingerprintController.dispose();
    _chassisNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouses() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.canManageInventory) {
        final warehouses = await _dataService.getWarehouses(isActive: true);
        setState(() {
          _warehouses = warehouses;
          if (_warehouses.isNotEmpty) {
            _selectedWarehouseId = _warehouses.first.id;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المخازن: $e', isError: true);
      }
    }
  }

  Future<void> _captureMotorFingerprint() async {
    try {
      // Check and request camera permissions
      final permissions = await _permissionService.requestCameraAndStoragePermissions();
      if (!permissions['camera']! || !permissions['storage']!) {
        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'يحتاج التطبيق لصلاحية الكاميرا لالتقاط الصور',
            isError: true
          );
        }
        return;
      }

      setState(() {
        _isProcessingImage = true;
      });

      if (!mounted) return;
      final image = await _imageService.takeMotorFingerprintPhoto(context);
      if (image != null && mounted) {
        // Validate image quality
        final isValidQuality = await _imageService.validateImageQuality(image);
        if (!isValidQuality) {
          throw 'جودة الصورة غير مناسبة. يرجى التأكد من الإضاءة الجيدة ووضوح الصورة';
        }

        // Extract motor fingerprint using enhanced OCR
        final extractedData = await _imageService.extractMotorFingerprintFromImage(image);

        if (extractedData.isEmpty || !extractedData.containsKey('motorFingerprint')) {
          throw 'لم يتم العثور على بصمة موتور في الصورة. يرجى التأكد من وضوح البصمة';
        }

        final motorFingerprint = extractedData['motorFingerprint']!;
        final confidence = double.tryParse(extractedData['confidence'] ?? '0') ?? 0.0;
        final method = extractedData['method'] ?? 'enhanced';

        if (motorFingerprint.isEmpty) {
          throw 'لم يتم استخراج بصمة الموتور بنجاح. يرجى المحاولة مرة أخرى';
        }

        setState(() {
          _motorFingerprintImage = image;
          _motorFingerprintController.text = motorFingerprint;
        });

        if (mounted) {
          String confidenceText;
          String methodText;

          if (method == 'enhanced') {
            confidenceText = confidence > 0.7
                ? 'بثقة عالية'
                : confidence > 0.5
                    ? 'بثقة متوسطة'
                    : 'بثقة منخفضة';
            methodText = 'تم استخراج بصمة الموتور (حروف وأرقام)';
          } else {
            confidenceText = 'بثقة منخفضة';
            methodText = 'تم استخراج النص الأساسي';
          }

          AppUtils.showSnackBar(
            context,
            '$methodText $confidenceText. يرجى مراجعة البيانات وتعديلها إذا لزم الأمر'
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, e.toString(), isError: true);
      }
    } finally {
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  Future<void> _captureChassisNumber() async {
    try {
      // Check and request camera permissions
      final permissions = await _permissionService.requestCameraAndStoragePermissions();
      if (!permissions['camera']! || !permissions['storage']!) {
        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'يحتاج التطبيق لصلاحية الكاميرا لالتقاط الصور',
            isError: true
          );
        }
        return;
      }

      setState(() {
        _isProcessingChassisImage = true;
      });

      if (!mounted) return;
      final image = await _imageService.takeChassisPhoto(context);
      if (image != null && mounted) {
        // Validate image quality
        final isValidQuality = await _imageService.validateImageQuality(image);
        if (!isValidQuality) {
          throw 'جودة الصورة غير مناسبة. يرجى التأكد من الإضاءة الجيدة ووضوح الصورة';
        }

        // Extract text using OCR
        final ocrResult = await OCRService.instance.extractTextFromImage(image);

        if (ocrResult.text.isEmpty) {
          throw 'لم يتم العثور على نص في الصورة. يرجى التأكد من وضوح رقم الشاسيه';
        }

        // Use extracted data if available, otherwise use raw text
        String finalText = ocrResult.text;
        if (ocrResult.isChassisNumber && ocrResult.extractedData.isNotEmpty) {
          // Prefer extracted chassis number
          finalText = ocrResult.extractedData['chassis_number'] ?? ocrResult.text;
        }

        setState(() {
          _chassisImage = image;
          _chassisNumberController.text = finalText;
        });

        if (mounted) {
          final confidenceText = ocrResult.confidence > 0.7
              ? 'بثقة عالية'
              : ocrResult.confidence > 0.5
                  ? 'بثقة متوسطة'
                  : 'بثقة منخفضة';

          AppUtils.showSnackBar(
            context,
            'تم استخراج رقم الشاسيه $confidenceText. يرجى مراجعة النص وتعديله إذا لزم الأمر'
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, e.toString(), isError: true);
      }
    } finally {
      setState(() {
        _isProcessingChassisImage = false;
      });
    }
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    // For editing, images are optional (can keep existing ones)
    final isEditing = widget.editItem != null;

    if (!isEditing && _motorFingerprintImage == null) {
      AppUtils.showSnackBar(context, 'يرجى التقاط صورة بصمة الموتور', isError: true);
      return;
    }
    if (!isEditing && _chassisImage == null) {
      AppUtils.showSnackBar(context, 'يرجى التقاط صورة رقم الشاسيه', isError: true);
      return;
    }
    if (_selectedWarehouseId == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار المخزن', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Upload images (only if new images are provided)
      String motorImageUrl = isEditing ? widget.editItem!.motorFingerprintImageUrl : '';
      String chassisImageUrl = isEditing ? widget.editItem!.chassisImageUrl : '';

      if (_motorFingerprintImage != null) {
        motorImageUrl = await _imageService.uploadImage(
          _motorFingerprintImage!,
          'motor_fingerprints_${DateTime.now().millisecondsSinceEpoch}',
        ) ?? '';
      }

      if (_chassisImage != null) {
        chassisImageUrl = await _imageService.uploadImage(
          _chassisImage!,
          'chassis_images_${DateTime.now().millisecondsSinceEpoch}',
        ) ?? '';
      }

      final now = DateTime.now();

      if (isEditing) {
        // Update existing item
        final updatedItem = widget.editItem!.copyWith(
          type: _typeController.text.trim(),
          model: _modelController.text.trim(),
          color: _colorController.text.trim(),
          brand: _brandController.text.trim(),
          countryOfOrigin: _countryController.text.trim(),
          yearOfManufacture: int.parse(_yearController.text.trim()),
          purchasePrice: double.parse(_purchasePriceController.text.trim()),
          suggestedSellingPrice: double.parse(_sellingPriceController.text.trim()),
          motorFingerprintImageUrl: motorImageUrl,
          motorFingerprintText: _motorFingerprintController.text.trim(),
          chassisImageUrl: chassisImageUrl,
          chassisNumber: _chassisNumberController.text.trim(),
          currentWarehouseId: _selectedWarehouseId!,
          updatedAt: now,
        );

        await _dataService.updateItem(updatedItem);

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم تحديث الصنف بنجاح');
          Navigator.of(context).pop(true);
        }
      } else {
        // Create new item
        final item = ItemModel(
          id: _motorFingerprintController.text.trim(),
          type: _typeController.text.trim(),
          model: _modelController.text.trim(),
          color: _colorController.text.trim(),
          brand: _brandController.text.trim(),
          countryOfOrigin: _countryController.text.trim(),
          yearOfManufacture: int.parse(_yearController.text.trim()),
          purchasePrice: double.parse(_purchasePriceController.text.trim()),
          suggestedSellingPrice: double.parse(_sellingPriceController.text.trim()),
          motorFingerprintImageUrl: motorImageUrl,
          motorFingerprintText: _motorFingerprintController.text.trim(),
          chassisImageUrl: chassisImageUrl,
          chassisNumber: _chassisNumberController.text.trim(),
          currentWarehouseId: _selectedWarehouseId!,
          status: 'متاح',
          createdAt: now,
          updatedAt: now,
          createdBy: authProvider.currentUser!.id,
        );

        await _dataService.createItem(item);

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم إضافة الصنف بنجاح');
          Navigator.of(context).pop(true);
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          isEditing ? 'خطأ في تحديث الصنف: $e' : 'خطأ في إضافة الصنف: $e',
          isError: true
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.editItem != null ? 'تعديل الصنف' : 'إضافة صنف جديد'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildMotorFingerprintSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildChassisSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildBasicInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPricingSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildWarehouseSection(),
              const SizedBox(height: AppConstants.largePadding * 2),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMotorFingerprintSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بصمة الموتور',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Image capture button
            SizedBox(
              width: double.infinity,
              height: 200,
              child: _motorFingerprintImage != null
                  ? Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                            image: DecorationImage(
                              image: FileImage(_motorFingerprintImage!),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: IconButton(
                            onPressed: _isProcessingImage ? null : _captureMotorFingerprint,
                            icon: const Icon(Icons.camera_alt),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.black54,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    )
                  : InkWell(
                      onTap: _isProcessingImage ? null : _captureMotorFingerprint,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                            style: BorderStyle.solid,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (_isProcessingImage)
                              const CircularProgressIndicator()
                            else
                              Icon(
                                Icons.camera_alt,
                                size: 48,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            const SizedBox(height: AppConstants.smallPadding),
                            Text(
                              _isProcessingImage 
                                  ? 'جاري معالجة الصورة...'
                                  : 'اضغط لالتقاط صورة بصمة الموتور',
                              style: Theme.of(context).textTheme.bodyLarge,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Extracted text field
            TextFormField(
              controller: _motorFingerprintController,
              decoration: const InputDecoration(
                labelText: 'النص المستخرج من بصمة الموتور',
                hintText: 'سيتم استخراج النص تلقائياً من الصورة',
                helperText: 'يمكنك تعديل النص المستخرج إذا لزم الأمر',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'بصمة الموتور مطلوبة';
                }
                if (value.trim().length < 5) {
                  return 'بصمة الموتور يجب أن تكون 5 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChassisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رقم الشاسيه',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Camera capture section
            GestureDetector(
              onTap: _isProcessingChassisImage ? null : _captureChassisNumber,
              child: Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _chassisImage != null ? Colors.green : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[100],
                ),
                child: _chassisImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.file(
                          _chassisImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _isProcessingChassisImage
                                ? Icons.hourglass_empty
                                : Icons.camera_alt,
                            size: 48,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            _isProcessingChassisImage
                                ? 'جاري معالجة الصورة...'
                                : 'اضغط لالتقاط صورة رقم الشاسيه',
                            style: Theme.of(context).textTheme.bodyLarge,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Extracted text field
            TextFormField(
              controller: _chassisNumberController,
              decoration: const InputDecoration(
                labelText: 'النص المستخرج من رقم الشاسيه',
                hintText: 'سيتم استخراج النص تلقائياً من الصورة',
                helperText: 'يمكنك تعديل النص المستخرج إذا لزم الأمر',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رقم الشاسيه مطلوب';
                }
                if (value.trim().length < 5) {
                  return 'رقم الشاسيه يجب أن يكون 5 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Vehicle type dropdown
            DropdownButtonFormField<String>(
              value: _typeController.text.isEmpty ? null : _typeController.text,
              decoration: const InputDecoration(
                labelText: 'نوع المركبة',
              ),
              items: _vehicleTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _typeController.text = value ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'نوع المركبة مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Brand field
            TextFormField(
              controller: _brandController,
              decoration: const InputDecoration(
                labelText: 'الماركة',
                hintText: 'مثال: هوندا، ياماها، سوزوكي',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الماركة مطلوبة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Model field
            TextFormField(
              controller: _modelController,
              decoration: const InputDecoration(
                labelText: 'الموديل',
                hintText: 'مثال: CBR 150، YBR 125',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الموديل مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Color field
            TextFormField(
              controller: _colorController,
              decoration: const InputDecoration(
                labelText: 'اللون',
                hintText: 'مثال: أحمر، أزرق، أسود',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اللون مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                // Country field
                Expanded(
                  child: TextFormField(
                    controller: _countryController,
                    decoration: const InputDecoration(
                      labelText: 'بلد المنشأ',
                      hintText: 'مثال: اليابان، الصين',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'بلد المنشأ مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                // Year field
                Expanded(
                  child: TextFormField(
                    controller: _yearController,
                    decoration: const InputDecoration(
                      labelText: 'سنة الصنع',
                      hintText: '2024',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سنة الصنع مطلوبة';
                      }
                      final year = int.tryParse(value.trim());
                      if (year == null) {
                        return 'سنة غير صحيحة';
                      }
                      final currentYear = DateTime.now().year;
                      if (year < 1990 || year > currentYear + 1) {
                        return 'سنة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأسعار',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                // Purchase price field
                Expanded(
                  child: TextFormField(
                    controller: _purchasePriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الشراء (ج.م)',
                      hintText: '50000',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سعر الشراء مطلوب';
                      }
                      final price = double.tryParse(value.trim());
                      if (price == null || price <= 0) {
                        return 'سعر غير صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                // Selling price field
                Expanded(
                  child: TextFormField(
                    controller: _sellingPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر البيع المقترح (ج.م)',
                      hintText: '55000',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سعر البيع مطلوب';
                      }
                      final sellingPrice = double.tryParse(value.trim());
                      if (sellingPrice == null || sellingPrice <= 0) {
                        return 'سعر غير صحيح';
                      }
                      final purchasePrice = double.tryParse(_purchasePriceController.text.trim());
                      if (purchasePrice != null && sellingPrice <= purchasePrice) {
                        return 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المخزن',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            DropdownButtonFormField<String>(
              value: _selectedWarehouseId,
              decoration: const InputDecoration(
                labelText: 'اختر المخزن',
              ),
              items: _warehouses.map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse.id,
                  child: Text(warehouse.name),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedWarehouseId = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'المخزن مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveItem,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                widget.editItem != null ? 'تحديث الصنف' : 'حفظ الصنف',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }
}
