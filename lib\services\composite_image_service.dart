import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

enum CropFocus {
  center,
  top,
  bottom,
  left,
  right,
}

class CompositeImageService {
  // Singleton pattern
  static final CompositeImageService _instance = CompositeImageService._internal();
  factory CompositeImageService() => _instance;
  CompositeImageService._internal();
  static CompositeImageService get instance => _instance;

  /// Create simple composite image - just 3 images stacked vertically with no spacing
  Future<File> createCompositeImage({
    required dynamic invoice,
    required dynamic item,
    required String motorFingerprintImagePath,
    required String chassisImagePath,
    required String customerIdImagePath,
  }) async {
    try {
      if (kDebugMode) {
        print('🖼️ Creating composite image with paths:');
        print('   Motor fingerprint: $motorFingerprintImagePath');
        print('   Chassis: $chassisImagePath');
        print('   Customer ID: $customerIdImagePath');
      }

      // Load images
      final motorImage = await _loadImageFromFile(motorFingerprintImagePath);
      final customerIdImage = await _loadImageFromFile(customerIdImagePath);

      ui.Image? chassisImage;
      if (chassisImagePath.isNotEmpty) {
        try {
          chassisImage = await _loadImageFromFile(chassisImagePath);
          if (kDebugMode) {
            print('✅ Chassis image loaded successfully: ${chassisImage.width}x${chassisImage.height}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Warning: Could not load chassis image from "$chassisImagePath": $e');
          }
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Chassis image path is empty - will create composite with 2 images only');
        }
      }

      // Create simple composite image - only use 3 different images
      final compositeImage = await _createSimpleComposite(
        motorImage: motorImage,
        chassisImage: chassisImage, // Can be null if not available
        customerIdImage: customerIdImage,
        invoiceNumber: invoice.invoiceNumber,
      );

      // Save to file
      final file = await _saveImageToFile(compositeImage, invoice.invoiceNumber);

      if (kDebugMode) {
        print('✅ Simple composite image created: ${file.path}');
        print('   Size: ${compositeImage.width}x${compositeImage.height}');
        print('   File size: ${(await file.length() / 1024).toStringAsFixed(1)} KB');
      }

      return file;
    } catch (e) {
      throw Exception('Failed to create composite image: $e');
    }
  }

  /// Load image from file path
  Future<ui.Image> _loadImageFromFile(String imagePath) async {
    final file = File(imagePath);
    final bytes = await file.readAsBytes();
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// Create simple composite - 3 large images filling screen with header
  Future<ui.Image> _createSimpleComposite({
    required ui.Image motorImage,
    ui.Image? chassisImage, // Can be null if not available
    required ui.Image customerIdImage,
    required String invoiceNumber,
  }) async {
    // Simple vertical layout - 3 images stacked with no spacing
    const double canvasWidth = 1080;  // Full width
    const double headerHeight = 80;   // Small header for invoice number

    // Equal height for all images - simple and clean
    const double imageHeight = 400;   // Same height for all images

    // Calculate canvas height based on available images
    final int imageCount = chassisImage != null ? 3 : 2; // Only count available images
    final double canvasHeight = headerHeight + (imageHeight * imageCount);

    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder, Rect.fromLTWH(0, 0, canvasWidth, canvasHeight));

    // White background
    final backgroundPaint = Paint()..color = Colors.white;
    canvas.drawRect(Rect.fromLTWH(0, 0, canvasWidth, canvasHeight), backgroundPaint);

    // Draw header with invoice number
    _drawSimpleHeader(canvas, canvasWidth, headerHeight, invoiceNumber);

    // Draw the three images stacked vertically with no spacing - simple stretch
    double currentY = headerHeight;

    // Motor fingerprint image (top) - stretched to fit
    canvas.drawImageRect(
      motorImage,
      Rect.fromLTWH(0, 0, motorImage.width.toDouble(), motorImage.height.toDouble()),
      Rect.fromLTWH(0, currentY, canvasWidth, imageHeight),
      Paint()..filterQuality = FilterQuality.high,
    );
    currentY += imageHeight;

    // Chassis image (middle) - only draw if available
    if (chassisImage != null) {
      canvas.drawImageRect(
        chassisImage,
        Rect.fromLTWH(0, 0, chassisImage.width.toDouble(), chassisImage.height.toDouble()),
        Rect.fromLTWH(0, currentY, canvasWidth, imageHeight),
        Paint()..filterQuality = FilterQuality.high,
      );
      currentY += imageHeight;
    }

    // Customer ID image (bottom) - stretched to fit
    canvas.drawImageRect(
      customerIdImage,
      Rect.fromLTWH(0, 0, customerIdImage.width.toDouble(), customerIdImage.height.toDouble()),
      Rect.fromLTWH(0, currentY, canvasWidth, imageHeight),
      Paint()..filterQuality = FilterQuality.high,
    );

    final picture = recorder.endRecording();
    return await picture.toImage(canvasWidth.toInt(), canvasHeight.toInt());
  }

  /// Draw simple header with invoice number only
  void _drawSimpleHeader(Canvas canvas, double width, double height, String invoiceNumber) {
    // Header background with gradient
    final headerGradient = ui.Gradient.linear(
      const Offset(0, 0),
      Offset(0, height),
      [
        const Color(0xFF1976D2), // Blue
        const Color(0xFF1565C0), // Darker blue
      ],
    );

    final headerPaint = Paint()..shader = headerGradient;
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), headerPaint);

    // Invoice number text
    final invoiceTextPainter = TextPainter(
      text: TextSpan(
        text: 'رقم الفاتورة: $invoiceNumber',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
          fontFamily: 'Arial',
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    invoiceTextPainter.layout();
    invoiceTextPainter.paint(
      canvas,
      Offset(width - invoiceTextPainter.width - 20, (height - invoiceTextPainter.height) / 2),
    );
  }

  /// Save image to file with unique identifier
  Future<File> _saveImageToFile(ui.Image image, String invoiceNumber) async {
    final directory = await getApplicationDocumentsDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomId = (timestamp % 100000).toString().padLeft(5, '0'); // 5-digit random ID
    final fileName = 'composite_${invoiceNumber}_${timestamp}_$randomId.jpg';
    final file = File('${directory.path}/$fileName');

    if (kDebugMode) {
      print('💾 Saving composite image: $fileName');
    }

    // Save as PNG for now (JPG encoding would require additional package)
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();
    await file.writeAsBytes(bytes);

    return file;
  }

  /// Share composite image - simple sharing without message
  Future<void> shareCompositeImage(File imageFile, {String? message}) async {
    try {
      await Share.shareXFiles([XFile(imageFile.path)]);

      if (kDebugMode) {
        print('✅ Composite image shared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to share composite image: $e');
      }
      throw Exception('فشل في مشاركة الصورة: $e');
    }
  }

  /// Show share dialog - simplified
  Future<void> showShareDialog(BuildContext context, File imageFile) async {
    await shareCompositeImage(imageFile);
  }

  /// Share to WhatsApp - simplified
  Future<void> shareToWhatsApp(File imageFile, {String? phoneNumber, String? message}) async {
    try {
      await Share.shareXFiles([XFile(imageFile.path)]);

      if (kDebugMode) {
        print('✅ Image shared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to share: $e');
      }
      throw Exception('فشل في المشاركة: $e');
    }
  }


}