import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AppUtils {
  // Date formatting
  static String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }
  
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
  
  static String formatTime(DateTime time) {
    return DateFormat('HH:mm').format(time);
  }
  
  // Currency formatting with clean Arabic display
  static String formatCurrency(dynamic amount) {
    final double value = (amount is int) ? amount.toDouble() : (amount as double? ?? 0.0);

    // Check if the value is a whole number
    if (value == value.roundToDouble()) {
      // Format as integer (no decimal places)
      final formatter = NumberFormat('#,##0', 'en_US');
      final formattedNumber = formatter.format(value.round());
      return '$formattedNumber ج.م';
    } else {
      // Format with decimal places
      final formatter = NumberFormat('#,##0.00', 'en_US');
      final formattedNumber = formatter.format(value);
      return '$formattedNumber ج.م';
    }
  }

  // Number formatting
  static String formatNumber(dynamic number) {
    final double value = (number is int) ? number.toDouble() : (number as double? ?? 0.0);

    // Check if the value is a whole number
    if (value == value.roundToDouble()) {
      // Format as integer (no decimal places)
      final formatter = NumberFormat('#,##0', 'en_US');
      return formatter.format(value.round());
    } else {
      // Format with decimal places (up to 2)
      final formatter = NumberFormat('#,##0.##', 'en_US');
      return formatter.format(value);
    }
  }

  // Safe conversion helpers
  static double toDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static int toInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }
  
  // Generate unique IDs
  static String generateInvoiceId(String type) {
    final now = DateTime.now();
    final dateStr = DateFormat('yyMMdd').format(now);
    final timeStr = DateFormat('HHmmss').format(now);
    return '$type-$dateStr-$timeStr';
  }

  static String generateId() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return '${timestamp}_$random';
  }

  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  static String generateReceiptId() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyMMdd').format(now);
    final timeStr = DateFormat('HHmmss').format(now);
    return 'RCPT-$dateStr-$timeStr';
  }
  
  static String generateInventoryId() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyMMdd').format(now);
    final timeStr = DateFormat('HHmmss').format(now);
    return 'IN-$dateStr-$timeStr';
  }
  
  static String generateReturnId() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyMMdd').format(now);
    final timeStr = DateFormat('HHmmss').format(now);
    return 'RET-$dateStr-$timeStr';
  }
  
  // Validation helpers
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  static bool isValidPhone(String phone) {
    return RegExp(r'^01[0-2,5]{1}[0-9]{8}$').hasMatch(phone);
  }
  
  static bool isValidNationalId(String nationalId) {
    return RegExp(r'^[0-9]{14}$').hasMatch(nationalId);
  }
  
  // Show snackbar with enhanced error handling
  static void showSnackBar(BuildContext context, String message, {bool isError = false}) {
    try {
      // Check if context is still mounted and has a Scaffold
      if (!context.mounted) return;

      // Use post frame callback to ensure the widget tree is built
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          try {
            ScaffoldMessenger.of(context).clearSnackBars();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  message,
                  style: const TextStyle(color: Colors.white),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                backgroundColor: isError ? Colors.red.shade600 : Colors.green.shade600,
                behavior: SnackBarBehavior.fixed, // Changed from floating to fixed
                margin: null, // Remove margin for fixed behavior
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: Duration(seconds: isError ? 4 : 2),
                action: SnackBarAction(
                  label: 'إغلاق',
                  textColor: Colors.white,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                ),
              ),
            );
          } catch (e) {
            // Fallback: print to console if SnackBar fails
            if (kDebugMode) {
              print('${isError ? '❌' : '✅'} $message');
            }
          }
        }
      });
    } catch (e) {
      // Ultimate fallback
      if (kDebugMode) {
        print('${isError ? '❌' : '✅'} $message');
      }
    }
  }

  // Show confirmation dialog
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // Show loading dialog
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 20),
            Text(message ?? 'جاري التحميل...'),
          ],
        ),
      ),
    );
  }
  
  // Hide loading dialog
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }
  
  // Show confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
  
  // Calculate profit percentage
  static double calculateProfitPercentage(double cost, double sellingPrice) {
    if (cost == 0) return 0;
    return ((sellingPrice - cost) / cost) * 100;
  }
  
  // Calculate profit amount
  static double calculateProfitAmount(double cost, double sellingPrice) {
    return sellingPrice - cost;
  }
}
